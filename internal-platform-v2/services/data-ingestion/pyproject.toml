# SPDX-FileCopyrightText: Terrafloww Labs, 2025

[project]
name = "terrafloww-data-ingestion"
version = "1.0.0"
description = "Modular data ingestion framework for Terrafloww platform"
authors = [
    {name = "Terrafloww Labs", email = "<EMAIL>"}
]
requires-python = ">=3.12"
license = {text = "Terrafloww Labs Proprietary"}

dependencies = [
    # Core dependencies
    "boto3>=1.29.0",
    "s3fs>=2024.6.0",
    "deltalake==0.25.5",
    "pandas>=2.0.0",
    "pyarrow>=14.0.0",

    # STAC dependencies
    "pystac-client>=0.7.0",
    "httpx>=0.25.0",

    # Additional dependencies for COG parsing
    "shapely>=2.0",
    "rasterio>=1.3",
    "affine>=2.3",
    "pyproj>=3.0",

    # Async support
    "asyncio-mqtt>=0.13.0",

    # Configuration and utilities
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",

    # Logging and monitoring
    "structlog>=23.0.0",

    # Kubernetes client (for future SQS consumer)
    "kubernetes>=28.0.0",

    # HTTP client for APIs
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]

[project.scripts]
data-ingestion = "main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["framework", "plugins", "consumers"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]

[tool.black]
line-length = 100
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.ruff]
line-length = 100
target-version = "py312"

