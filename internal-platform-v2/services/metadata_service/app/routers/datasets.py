# services/metadata_service/app/routers/datasets.py
from fastapi import (
    APIRouter,
    HTTPException,
    Body,
)  # Add Depends if needed for auth later
from typing import Optional  # Use specific types

# Import Pydantic models and CRUD functions
from terrafloww.api.models import (
    CommitDatasetPayload,
    DatasetInfoResponse,
    DatasetDefinitionQuery,
    DatasetExistsResponse,
)
from terrafloww.api.models import datasets as datasets_crud


router = APIRouter(prefix="/datasets", tags=["Datasets"])


@router.post("", response_model=DatasetInfoResponse, status_code=201)
async def commit_new_dataset_version(payload: CommitDatasetPayload = Body(...)):
    """Registers a new version of a dataset."""
    # Extract artifact versions map
    artifact_versions_dict = (
        payload.source_artifact_versions.root
        if payload.source_artifact_versions
        else None
    )

    # Convert hex schema back to bytes if it exists
    schema_bytes = bytes.fromhex(payload.arrow_schema) if payload.arrow_schema else None

    try:
        result_dict = await datasets_crud.commit_dataset_version(
            name=payload.name,
            dataset_type=payload.dataset_type,
            source_job_id=payload.source_job_id,
            source_artifact_versions=artifact_versions_dict,
            # Pass new fields
            storage_paths=payload.storage_paths,
            arrow_schema=schema_bytes,
            raw_cog_urls=payload.raw_cog_urls,
            cog_tile_offsets=payload.cog_tile_offsets,
            cog_tile_byte_counts=payload.cog_tile_byte_counts,
            cog_grid_ids=payload.cog_grid_ids,
            cog_dtype=payload.cog_dtype,
            cog_predictor=payload.cog_predictor,
            cog_scale=payload.cog_scale,
            cog_offset=payload.cog_offset,
            # Pass common fields
            parameters=payload.parameters,
            quality_metrics=payload.quality_metrics,
            raw_properties=payload.raw_properties,
            raw_geometry=payload.raw_geometry,
            # status defaults to "available" in crud
        )
        # Result should already match response model structure
        # Pydantic will validate the returned dict against DatasetInfoResponse
        return result_dict
    except ValueError as e:  # Catch validation errors from CRUD/Pydantic
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"API Error committing dataset '{payload.name}': {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail="Internal server error during dataset commit."
        )


@router.get(
    "/{name}/{version}",
    response_model=DatasetInfoResponse,
    responses={404: {"description": "Dataset version not found"}},
)
async def get_dataset_version_info(
    name: str, version: str
):  # Keep version as str for 'latest'
    """Gets metadata for a specific dataset version ('latest' or integer)."""
    load_version: Optional[int] = None
    if version.lower() != "latest":
        try:
            load_version = int(version)
        except ValueError:
            raise HTTPException(
                status_code=400, detail="Version must be an integer or 'latest'"
            )

    info_dict = await datasets_crud.get_dataset_info(name, load_version or "latest")
    if info_dict is None:
        raise HTTPException(
            status_code=404, detail=f"Dataset '{name}' version '{version}' not found."
        )

    # Pydantic performs response validation based on the returned dict
    return info_dict


# --- NEW: Endpoint to query dataset by definition ---
@router.post("/query/by_definition", response_model=DatasetExistsResponse)
async def find_dataset_by_definition_endpoint(
    query: DatasetDefinitionQuery = Body(...),
):
    """Checks if a dataset matching the definition (sources, params) exists."""
    result = await datasets_crud.find_dataset_by_definition(query)
    if result is None:
        # Indicates an error during the query process
        raise HTTPException(
            status_code=500, detail="Error querying dataset definition."
        )
    return result  # Returns {"exists": True/False, ...}
