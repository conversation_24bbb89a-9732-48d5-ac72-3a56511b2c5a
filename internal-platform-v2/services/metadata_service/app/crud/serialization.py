"""Serialization utilities for Delta table storage."""

import json
from typing import Any, Optional


def serialize_for_storage(obj: Any) -> Optional[str]:
    """
    Serializes an object to JSON for storage in a Delta table.
    Returns None if the object is None or empty.
    
    Args:
        obj: Any JSON-serializable object to serialize
        
    Returns:
        A JSON string representation, or None if the input is None or empty
    """
    if obj is None or (isinstance(obj, (dict, list)) and len(obj) == 0):
        return None
        
    return json.dumps(obj, sort_keys=True)


def deserialize_from_storage(json_str: Optional[str]) -> Any:
    """
    Deserializes a JSON string from storage.
    Returns None if the string is None or empty.
    
    Args:
        json_str: A JSON string to deserialize
        
    Returns:
        The deserialized Python object, or None if the input is None or empty
    """
    if not json_str:
        return None
        
    return json.loads(json_str)
