# services/metadata_service/app/crud/grid_templates.py
import pyarrow as pa
import pyarrow.dataset as ds
from deltalake import write_deltalake
from datetime import datetime, timezone
from typing import Dict, Optional, Any

from tfw_raster_schemas import GRID_TEMPLATES_SCHEMA
from .delta_utils import (
    GRID_TEMPLATES_TABLE_PATH,
    get_delta_table,
)


async def add_grid_template(template_data: Dict[str, Any]) -> str:
    """
    Adds a new grid template definition if it doesn't exist based on grid_id.
    Returns the grid_id of the added or existing template.
    """
    # Expects template_data to have keys matching GRID_TEMPLATES_SCHEMA fields
    # Requires grid_id to be pre-generated by the caller (e.g., ingester)
    if "grid_id" not in template_data:
        raise ValueError("grid_id must be provided in template_data")

    grid_id = template_data["grid_id"]

    # Check if it already exists
    existing = await get_grid_template(grid_id)
    if existing:
        # TODO: Optionally validate if existing data matches new data?
        print(f"Grid template {grid_id} already exists.")
        return grid_id

    # Add created_at timestamp
    template_data["created_at"] = datetime.now(timezone.utc)

    # Prepare data for PyArrow Table
    data_for_table = {key: [value] for key, value in template_data.items()}

    try:
        table = pa.Table.from_pydict(data_for_table, schema=GRID_TEMPLATES_SCHEMA)
    except (pa.ArrowInvalid, TypeError) as e:
        print(f"Schema mismatch creating grid template table for {grid_id}: {e}")
        print(f"Data provided: {template_data}")
        raise ValueError(f"Schema mismatch creating grid template table: {e}")

    storage_options = None  # Adjust for cloud
    write_deltalake(
        GRID_TEMPLATES_TABLE_PATH,
        table,
        mode="append",
        engine="rust",
        # schema_mode="ignore", # Append requires matching schema, 'ignore' might be safer if table exists
        name=f"Add grid template {grid_id}",
        storage_options=storage_options,
    )
    print(f"Added new grid template: {grid_id}")
    return grid_id


async def get_grid_template(grid_id: str) -> Optional[Dict[str, Any]]:
    """Retrieves a grid template definition by its ID."""
    dt = get_delta_table(GRID_TEMPLATES_TABLE_PATH)
    if not dt:
        return None

    try:
        table = dt.to_pyarrow_table(filters=(ds.field("grid_id") == grid_id))
        if len(table) == 0:
            return None

        # Assume grid_id is unique, take the first row
        template_dict = table.to_pylist()[0]

        # Convert transform list back to tuple if needed for Pydantic
        if "transform" in template_dict and isinstance(
            template_dict["transform"], list
        ):
            template_dict["transform"] = tuple(template_dict["transform"])

        # Convert timestamp
        if "created_at" in template_dict and isinstance(
            template_dict["created_at"], pa.TimestampScalar
        ):
            template_dict["created_at"] = template_dict["created_at"].as_py()

        return template_dict
    except Exception as e:
        print(f"Error retrieving grid template {grid_id}: {e}")
        return None
