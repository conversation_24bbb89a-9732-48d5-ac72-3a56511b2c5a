# services/metadata_service/app/crud/delta_utils.py
import os
from pathlib import Path
from deltalake import DeltaTable, write_deltalake
from deltalake.exceptions import TableNotFoundError, DeltaError
import pyarrow as pa

# Import schemas from their canonical locations
from tfw_raster_schemas import IMG_DATASETS_SCHEMA, GRID_TEMPLATES_SCHEMA

# Import local schemas
from ..models.schemas import JOBS_SCHEMA

# --- Configuration ---
# Use environment variables or a config file for flexibility
DELTA_LAKE_ROOT = os.environ.get("DELTA_LAKE_ROOT", "/tmp/platform_delta_tables")
# Ensure root directory exists
Path(DELTA_LAKE_ROOT).mkdir(parents=True, exist_ok=True)


DATASETS_TABLE_PATH = str(Path(DELTA_LAKE_ROOT) / "datasets")
JOBS_TABLE_PATH = str(Path(DELTA_LAKE_ROOT) / "jobs")
# MODELS_TABLE_PATH = str(Path(DELTA_LAKE_ROOT) / "models") # If needed later
GRID_TEMPLATES_TABLE_PATH = str(Path(DELTA_LAKE_ROOT) / "grid_templates")  # NEW Path


def get_delta_table(
    table_path: str, version: int | None = None
) -> DeltaTable | None:  # Return None on not found
    """Loads a DeltaTable, handling potential TableNotFoundError."""
    try:
        storage_options = None  # Adjust for cloud storage
        dt = DeltaTable(table_path, version=version, storage_options=storage_options)
        return dt
    except TableNotFoundError:
        print(f"Info: Delta table not found at {table_path}. Returning None.")
        return None
    except DeltaError as e:  # Catch specific Delta errors
        print(f"DeltaError loading table from {table_path}: {e}")
        return None  # Return None on Delta errors too for simplicity, or re-raise
    except Exception as e:
        print(f"Unexpected error loading Delta table from {table_path}: {e}")
        raise  # Re-raise unexpected errors


def ensure_table_exists(
    table_path: str, schema: pa.Schema, partition_cols: list[str] | None = None
):
    """Creates the Delta table if it doesn't exist."""
    # Check if the _delta_log directory exists as a better indicator
    delta_log_path = Path(table_path) / "_delta_log"
    if not delta_log_path.exists():
        print(f"Delta log not found. Creating new Delta table at {table_path}...")
        try:
            # Create an empty PyArrow Table with the schema
            empty_table = pa.Table.from_pylist([], schema=schema)
            storage_options = None  # Adjust for cloud storage
            write_deltalake(
                table_path,
                empty_table,
                mode="overwrite",  # Use overwrite to initialize with schema
                schema_mode="overwrite",  # Ensure schema is strictly applied
                partition_by=partition_cols,
                storage_options=storage_options,
                engine="rust",
            )
            print(f"Table {table_path} created successfully.")
        except Exception as e:
            print(f"ERROR: Failed to create table {table_path}: {e}")
            # Decide how to handle this - maybe raise?
            raise RuntimeError(f"Failed to initialize table {table_path}") from e
    # else: # Optional: Add schema evolution checks here if needed later
    # print(f"Table {table_path} already exists.")
    # dt = get_delta_table(table_path)
    # if dt and dt.schema() != schema:
    # print("Warning: Existing table schema differs. Consider migration or schema evolution settings.")
    # Add migration logic or adjust write_deltalake schema_mode based on policy


# Call ensure_table_exists for all tables at import time (or move to app startup)
ensure_table_exists(
    DATASETS_TABLE_PATH, IMG_DATASETS_SCHEMA, partition_cols=["name", "dataset_type"]
)
ensure_table_exists(JOBS_TABLE_PATH, JOBS_SCHEMA, partition_cols=["job_type"])
ensure_table_exists(
    GRID_TEMPLATES_TABLE_PATH, GRID_TEMPLATES_SCHEMA
)  # Ensure grid templates table
