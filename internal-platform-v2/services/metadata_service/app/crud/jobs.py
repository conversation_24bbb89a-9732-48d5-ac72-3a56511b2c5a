# services/metadata_service/app/crud/jobs.py
import pyarrow as pa
from deltalake import write_deltalake
import uuid
from datetime import datetime, timezone
from typing import Any
import pyarrow.dataset as ds

# Import schemas and utils
from ..models.schemas import JOBS_SCHEMA
from .delta_utils import (
    JOBS_TABLE_PATH,
    get_delta_table,
    ensure_table_exists,
)

# Ensure the table exists on module load
ensure_table_exists(JOBS_TABLE_PATH, JOBS_SCHEMA, partition_cols=["job_type"])


async def register_job(
    job_type: str,
    input_artifact_versions: dict[str, int],
    parameters: dict[str, str] | None,
) -> str:
    """Registers a new job with status 'submitted'."""
    job_id = str(uuid.uuid4())
    submit_time = datetime.now(timezone.utc)

    data = {
        "job_id": [job_id],
        "job_type": [job_type],
        "status": ["submitted"],
        "submit_time": [submit_time],
        "start_time": [None],
        "end_time": [None],
        "input_artifact_versions": [input_artifact_versions or {}],
        "output_dataset_id": [None],
        "parameters": [parameters or {}],
        "error_message": [None],
        "compute_engine": ["Ray"],  # Default or make configurable
    }

    try:
        table = pa.Table.from_pydict(data, schema=JOBS_SCHEMA)
    except pa.ArrowInvalid as e:
        print(f"Schema mismatch creating job table: {e}")
        raise ValueError(f"Schema mismatch: {e}")

    storage_options = None  # Adjust for cloud storage
    write_deltalake(
        JOBS_TABLE_PATH,
        table,
        mode="append",
        engine="rust",
        schema_mode="merge",
        partition_by=["job_type"],
        storage_options=storage_options,
        name=f"Register job {job_id}",
        description=f"Registered job {job_id} type {job_type}",
    )
    return job_id


async def update_job(
    job_id: str,
    status: str,
    output_dataset_id: str | None = None,
    error_message: str | None = None,
) -> bool:
    """Updates the status and timing of an existing job."""
    # This is trickier with Delta's append-only nature for updates.
    # Common patterns:
    # 1. Rewrite: Read the relevant partitions, update in memory, write back (complex).
    # 2. Append Status Events: Append new rows indicating status changes (makes querying harder).
    # 3. Delta MERGE: Use MERGE operation (requires unique constraint understanding).

    # Simple Approach for MVP (May not scale perfectly): Append a new row with updated status.
    # Querying latest status would require finding the latest timestamp for a job_id.
    # Let's *simulate* an update by just logging it for now.
    # A robust implementation would use Delta MERGE or a different storage strategy for mutable state.

    print("--- SIMULATING JOB UPDATE ---")
    print(f"Job ID: {job_id}")
    print(f"New Status: {status}")
    current_time = datetime.now(timezone.utc)
    update_info = {"update_time": current_time}

    if status == "running":
        update_info["start_time"] = current_time
        print(f"Start Time: {current_time}")
    elif status in ["completed", "failed"]:
        update_info["end_time"] = current_time
        print(f"End Time: {current_time}")

    if output_dataset_id:
        update_info["output_dataset_id"] = output_dataset_id
        print(f"Output Dataset ID: {output_dataset_id}")
    if error_message:
        update_info["error_message"] = error_message
        print(f"Error: {error_message}")
    print("--- END SIMULATION ---")

    # In a real implementation using MERGE (conceptual):
    # dt = get_delta_table(JOBS_TABLE_PATH)
    # updates_table = pa.Table.from_pydict({ 'job_id': [job_id], 'status': [status], ... }) # Include keys and updated values
    # dt.merge(
    #     source=updates_table,
    #     predicate='target.job_id = source.job_id',
    #     source_alias='source',
    #     target_alias='target'
    # ).when_matched_update_all().execute()

    # For now, just return True assuming the update *would* happen.
    return True


async def get_job_info(job_id: str) -> dict[str, Any] | None:
    """Gets the latest information for a specific job."""
    # Requires querying the latest status row for the job_id if using the append approach.
    # If using MERGE, a simple filter would work.

    # Simulating for now, assuming MERGE or similar query logic:
    print("--- SIMULATING GET JOB INFO ---")
    print(f"Job ID: {job_id}")
    dt = get_delta_table(JOBS_TABLE_PATH)
    if not dt:
        print("Jobs table not found.")
        return None
    try:
        # This filter assumes the 'update' pattern properly maintains one logical row per job_id
        table = dt.to_pyarrow_table(filters=(ds.field("job_id") == job_id))
        if len(table) == 0:
            print("Job not found.")
            return None
        # Assuming the last entry for a job ID reflects the latest state (if using append method)
        # If using MERGE, there should only be one row.
        latest_job_record = table.to_pylist()[-1]  # Simplistic selection
        print(f"Found Job Info: {latest_job_record}")
        print("--- END SIMULATION ---")
        return latest_job_record  # Convert to dict if needed by response model
    except Exception as e:
        print(f"Error querying job {job_id}: {e}")
        return None
