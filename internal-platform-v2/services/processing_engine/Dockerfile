# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Multi-stage Dockerfile for processing engine with UV
FROM python:3.12-slim AS base

# Install UV for fast Python package management (pinned version for reproducibility)
COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /usr/local/bin/

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install dependencies using UV with cache mount
COPY services/processing_engine/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system --no-cache -r requirements.txt

# Install platform libraries (assuming they're in the parent directory structure)
COPY ../../libs/tfw_engine_core /tmp/tfw_engine_core
COPY ../../libs/tfw_ray_utils /tmp/tfw_ray_utils
COPY ../../libs/tfw_core_utils /tmp/tfw_core_utils
COPY ../../libs/tfw_raster_schemas /tmp/tfw_raster_schemas
COPY ../../libs/tfw_processing_api /tmp/tfw_processing_api

RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system --no-cache \
    /tmp/tfw_engine_core \
    /tmp/tfw_ray_utils \
    /tmp/tfw_core_utils \
    /tmp/tfw_raster_schemas \
    /tmp/tfw_processing_api

# Copy application code
COPY services/processing_engine/app/ ./app/

# Expose ports for gRPC and Flight
EXPOSE 50051 50052

# Set environment variables
ENV PYTHONPATH=/app
ENV GRPC_PORT=50051
ENV FLIGHT_PORT=50052
ENV FLIGHT_SERVER_HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import grpc; import sys; sys.exit(0)" || exit 1

# Run the gRPC and Flight servers
CMD ["python", "-m", "app.main"]
