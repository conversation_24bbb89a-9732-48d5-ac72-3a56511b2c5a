"""gRPC service implementation for the Processing Engine."""

import logging
import uuid
import asyncio
import grpc
import os
from typing import Dict, List

# Import processing engine protos
from terrafloww.processing_engine.v1 import processing_engine_pb2
from terrafloww.processing_engine.v1 import processing_engine_pb2_grpc

# --- RAY IMPORT (INITIALIZATION HANDLED BY DRIVER) ---
# Import Ray but let the driver handle initialization to avoid conflicts
try:
    import ray
    # Ray initialization is handled by the RayDriver singleton to ensure consistent parameters
    logging.getLogger(__name__).info("Ray imported successfully - initialization handled by driver")
except ImportError:
    logging.getLogger(__name__).warning("Ray not available, will use fallback execution mode")

try:
    from terrafloww.engine_core.runtime_ray.driver import run_workflow_entrypoint
except ImportError:
    # Handle cases where the path might differ slightly depending on execution context
    # This might indicate an issue with PYTHONPATH or package installation
    logging.getLogger(__name__).critical("Could not import run_workflow_entrypoint. Check installation/PYTHONPATH.", exc_info=True)
    # Define a dummy function to allow server startup but fail requests
    async def run_workflow_entrypoint(exec_id, plan):
         raise RuntimeError("Driver entrypoint not imported correctly.")

# Configure logging
logger = logging.getLogger(__name__)

class ProcessingEngineServicer(processing_engine_pb2_grpc.ProcessingEngineServiceServicer):
    """Implementation of the ProcessingEngineService."""

    def __init__(self, flight_service=None):
        """Initialize the servicer."""
        # No need to instantiate driver here, entrypoint handles singleton
        self.logger = logging.getLogger(__name__)
        self.flight_service = flight_service # Keep reference if Flight needs info later

    # Make the method async as we use asyncio.create_task
    async def ExecuteWorkflow(self,
                              request: processing_engine_pb2.ExecuteWorkflowRequest,
                              context: grpc.aio.ServicerContext
                             ) -> processing_engine_pb2.ExecuteWorkflowResponse:
        """
        Execute a workflow. Accepts request, triggers background execution via RayDriver.
        
        This method handles incoming ExecuteWorkflowRequests, which contain a WorkflowPlan
        with function_id and parameters for each operation to be applied.
        """
        plan = request.plan
        execution_id = request.job_id if request.job_id and request.job_id != "" else str(uuid.uuid4())
        flight_ticket_str = f"ticket_for_{execution_id}" # Use a clear prefix
        
        # Log the incoming request details
        self.logger.info(f"Received ExecuteWorkflow request - execution_id: {execution_id}")
        self.logger.info(f"WorkflowPlan: load_step.collection='{plan.load_step.collection}', "
                       f"apply_steps={len(plan.apply_steps)}, head_limit={plan.head_limit}")
        
        # Log details about each apply step in the plan
        for i, step in enumerate(plan.apply_steps):
            self.logger.info(f"Apply step {i}: function_id='{step.function_id}', "
                            f"parameters={dict(step.parameters)}")

        self.logger.info(f"Received ExecuteWorkflow request. Execution ID: {execution_id}, Ticket: {flight_ticket_str}")
        self.logger.debug(f"Plan Details: {request.plan}") # Log plan details if needed

        try:
            # --- Trigger Background Execution via Driver Entrypoint ---
            # No need to initialize cache - Flight server manages state internally
            # Use asyncio.create_task for fire-and-forget execution.
            # The run_workflow_entrypoint function (and underlying driver) handles
            # Ray initialization, planning, task submission, and result caching/status updates.
            asyncio.create_task(run_workflow_entrypoint(execution_id, request.plan))

            self.logger.info(f"Task created for workflow execution ID: {execution_id}")

            # Return the response promptly
            return processing_engine_pb2.ExecuteWorkflowResponse(
                execution_id=execution_id,
                flight_ticket=flight_ticket_str, # Send the generated ticket back
                status_message="Workflow submitted for background execution."
            )
        # Catch potential errors during task creation itself, although unlikely
        except Exception as e:
            self.logger.error(f"Failed to create background task for ExecuteWorkflow {execution_id}: {e}", exc_info=True)
            # Use await context.abort for async gRPC methods [1]
            await context.abort(grpc.StatusCode.INTERNAL, f"Failed to schedule workflow: {str(e)}")
            # Fallback return (though abort should terminate)
            return processing_engine_pb2.ExecuteWorkflowResponse()

# --- Helper to add Servicer to Server (ensure async compatibility) ---
def add_ProcessingEngineService_to_server(servicer, server):
    """Add the servicer to the server."""
    processing_engine_pb2_grpc.add_ProcessingEngineServiceServicer_to_server(servicer, server)
