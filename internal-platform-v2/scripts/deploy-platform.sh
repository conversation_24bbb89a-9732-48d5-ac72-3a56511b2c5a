#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Unified deployment script for Terrafloww Platform
# Usage: ./deploy-platform.sh [component]
# Components: ray, processing-engine, all
# Best Practice: Uses Git SHA + timestamp for immutable image tags

set -e

# Configuration
NAMESPACE="terrafloww-platform"
REGISTRY="registry.digitalocean.com/terrafloww-dev"

# Generate immutable image tag based on git SHA + timestamp
GIT_SHA=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="${GIT_SHA}-${TIMESTAMP}"

# Component to deploy
COMPONENT="${1:-all}"

echo "🚀 Terrafloww Platform Deployment"
echo "=================================="
echo "Git SHA: $GIT_SHA"
echo "Timestamp: $TIMESTAMP"
echo "Image Tag: $IMAGE_TAG"
echo "Component: $COMPONENT"
echo "Namespace: $NAMESPACE"
echo ""

# Change to project root
cd "$(dirname "$0")/.."

deploy_ray() {
    echo "🔧 Deploying Ray Cluster..."
    
    # Build Ray custom image
    echo "📦 Building Ray custom image..."
    PUSH_IMAGE=true ./infra/k8s/build-ray-image.sh "${REGISTRY}/ray-custom:${IMAGE_TAG}"
    
    # Update Ray cluster configuration
    echo "🔧 Updating Ray cluster configuration..."
    sed -i "s|ray-custom:.*|ray-custom:${IMAGE_TAG}|g" infra/k8s/ray-cluster.yaml
    
    # Deploy Ray cluster
    echo "🚀 Deploying Ray cluster..."
    kubectl delete raycluster terrafloww-ray-cluster -n "$NAMESPACE" --ignore-not-found=true
    kubectl apply -f infra/k8s/ray-cluster.yaml
    
    # Wait for Ray cluster to be ready
    echo "⏳ Waiting for Ray cluster to be ready..."
    kubectl wait --for=condition=Ready pod -l ray.io/cluster=terrafloww-ray-cluster -n "$NAMESPACE" --timeout=300s
    
    echo "✅ Ray cluster deployed successfully!"
}

deploy_processing_engine() {
    echo "🔧 Deploying Processing Engine..."
    
    # Build processing engine image
    echo "📦 Building processing engine image..."
    docker build -f services/processing_engine/Dockerfile -t "${REGISTRY}/processing-engine:${IMAGE_TAG}" .
    
    # Push to registry
    echo "📤 Pushing processing engine image..."
    docker push "${REGISTRY}/processing-engine:${IMAGE_TAG}"
    
    # Update deployment configuration
    echo "🔧 Updating deployment configuration..."
    sed -i "s|processing-engine:.*|processing-engine:${IMAGE_TAG}|g" infra/k8s/processing-engine-deployment.yaml
    
    # Deploy processing engine
    echo "🚀 Deploying processing engine..."
    kubectl apply -f infra/k8s/processing-engine-deployment.yaml
    kubectl apply -f infra/k8s/processing-engine-service.yaml
    
    # Wait for deployment to be ready
    echo "⏳ Waiting for processing engine to be ready..."
    kubectl rollout status deployment/terrafloww-processing-engine -n "$NAMESPACE" --timeout=300s
    
    echo "✅ Processing engine deployed successfully!"
}

# Main deployment logic
case "$COMPONENT" in
    "ray")
        deploy_ray
        ;;
    "processing-engine")
        deploy_processing_engine
        ;;
    "all")
        deploy_ray
        echo ""
        deploy_processing_engine
        ;;
    *)
        echo "❌ Invalid component: $COMPONENT"
        echo "Valid components: ray, processing-engine, all"
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "Image Details:"
echo "  - Git SHA: $GIT_SHA"
echo "  - Timestamp: $TIMESTAMP"
echo "  - Ray Image: ${REGISTRY}/ray-custom:${IMAGE_TAG}"
echo "  - Processing Engine Image: ${REGISTRY}/processing-engine:${IMAGE_TAG}"
echo ""
echo "Next steps:"
echo "1. Test end-to-end workflow: cd sdk && python src/tests/test_ndvi.py"
echo "2. Check deployment status: kubectl get pods -n $NAMESPACE"
echo "3. Monitor logs if needed"
echo ""
echo "Troubleshooting:"
echo "  - Processing Engine logs: kubectl logs -n $NAMESPACE -l app=terrafloww,component=processing-engine"
echo "  - Ray cluster logs: kubectl logs -n $NAMESPACE -l ray.io/cluster=terrafloww-ray-cluster"
