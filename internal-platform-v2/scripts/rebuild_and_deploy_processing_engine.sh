#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Script to rebuild and redeploy the processing engine with S3 support

set -e

# Configuration
NAMESPACE="terrafloww-platform"
DEPLOYMENT_NAME="terrafloww-processing-engine"
IMAGE_NAME="registry.digitalocean.com/terrafloww-dev/processing-engine"

# Generate immutable image tag based on git SHA + timestamp
GIT_SHA=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="${GIT_SHA}-${TIMESTAMP}"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "🚀 Rebuilding and redeploying processing engine..."
echo "Git SHA: $GIT_SHA"
echo "Timestamp: $TIMESTAMP"
echo "Image Tag: $IMAGE_TAG"
echo "Full Image: $FULL_IMAGE_NAME"

# Change to the project root
cd "$(dirname "$0")/.."

echo "📦 Building new Docker image with S3 dependencies..."

# Build the Docker image
docker build \
    -f services/processing_engine/Dockerfile \
    -t "$FULL_IMAGE_NAME" \
    .

echo "✅ Docker image built successfully: $FULL_IMAGE_NAME"

# Push to registry
echo "📤 Pushing image to DigitalOcean Container Registry..."
docker push "$FULL_IMAGE_NAME"

echo "✅ Image pushed successfully"

# Update deployment with new image tag
echo "🔧 Updating deployment configuration with new image tag..."
sed -i "s|processing-engine:.*|processing-engine:${IMAGE_TAG}|g" infra/k8s/processing-engine-deployment.yaml

# Check if deployment exists
if ! kubectl get deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "📋 Deployment doesn't exist, applying deployment configuration..."
    kubectl apply -f infra/k8s/processing-engine-deployment.yaml
    kubectl apply -f infra/k8s/processing-engine-service.yaml
else
    echo "🔄 Applying updated deployment configuration..."
    kubectl apply -f infra/k8s/processing-engine-deployment.yaml
fi

echo "⏳ Waiting for deployment to be ready..."
kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=300s

echo "🔍 Checking deployment status..."
kubectl get pods -n "$NAMESPACE" -l app=terrafloww,component=processing-engine

echo "📋 Checking environment variables in the pod..."
POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l app=terrafloww,component=processing-engine -o jsonpath='{.items[0].metadata.name}')

if [[ -n "$POD_NAME" ]]; then
    echo "Pod: $POD_NAME"
    echo "Environment variables related to catalog:"
    kubectl exec -n "$NAMESPACE" "$POD_NAME" -- env | grep -E "(STAC_CATALOG|DO_)" || echo "No catalog environment variables found"
else
    echo "⚠️ No pods found for the processing engine"
fi

echo "🎉 Processing engine rebuild and deployment completed!"
echo ""
echo "Image Details:"
echo "  - Git SHA: $GIT_SHA"
echo "  - Timestamp: $TIMESTAMP"
echo "  - Full Image: $FULL_IMAGE_NAME"
echo ""
echo ""
echo "Troubleshooting:"
echo "  - Check logs: kubectl logs -n $NAMESPACE -l app=terrafloww,component=processing-engine"
echo "  - Check Ray connectivity: kubectl logs -n $NAMESPACE -l ray.io/cluster=terrafloww-ray-cluster"
