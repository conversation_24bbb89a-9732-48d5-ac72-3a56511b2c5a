#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Script to create Kubernetes secrets for catalog configuration
# This script reads from the DO credentials file and creates the K8s secret

set -e

# Configuration
NAMESPACE="terrafloww-platform"
SECRET_NAME="terrafloww-catalog-secrets"
CREDENTIALS_FILE="${CREDENTIALS_FILE:-/home/<USER>/Work/platform-build/do_key.env}"

echo "🔐 Setting up Kubernetes secrets for catalog configuration..."

# Check if credentials file exists
if [[ ! -f "$CREDENTIALS_FILE" ]]; then
    echo "❌ Credentials file not found: $CREDENTIALS_FILE"
    echo "Please ensure the file exists with the following format:"
    echo "do_access_key_id=your_access_key"
    echo "do_secret_access_key=your_secret_key"
    echo "do_space_endpoint=https://your-bucket.region.digitaloceanspaces.com"
    echo "do_region=your_region"
    exit 1
fi

# Source the credentials file
source "$CREDENTIALS_FILE"

# Validate required variables
if [[ -z "$do_access_key_id" || -z "$do_secret_access_key" || -z "$do_space_endpoint" || -z "$do_region" ]]; then
    echo "❌ Missing required credentials in $CREDENTIALS_FILE"
    echo "Required variables: do_access_key_id, do_secret_access_key, do_space_endpoint, do_region"
    exit 1
fi

# Extract bucket name from endpoint
# Format: https://bucket-name.region.digitaloceanspaces.com
BUCKET_NAME=$(echo "$do_space_endpoint" | sed -E 's|https://([^.]+)\..*|\1|')

echo "📋 Configuration:"
echo "  Bucket: $BUCKET_NAME"
echo "  Region: $do_region"
echo "  Endpoint: $do_space_endpoint"
echo "  Namespace: $NAMESPACE"

# Check if namespace exists
if ! kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
    echo "📦 Creating namespace: $NAMESPACE"
    kubectl create namespace "$NAMESPACE"
fi

# Delete existing secret if it exists
if kubectl get secret "$SECRET_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "🗑️ Deleting existing secret: $SECRET_NAME"
    kubectl delete secret "$SECRET_NAME" -n "$NAMESPACE"
fi

# Create the secret
echo "🔑 Creating Kubernetes secret: $SECRET_NAME"
kubectl create secret generic "$SECRET_NAME" \
    --namespace="$NAMESPACE" \
    --from-literal=bucket="$BUCKET_NAME" \
    --from-literal=endpoint="$do_space_endpoint" \
    --from-literal=region="$do_region" \
    --from-literal=access_key_id="$do_access_key_id" \
    --from-literal=secret_access_key="$do_secret_access_key"

# Label the secret
kubectl label secret "$SECRET_NAME" -n "$NAMESPACE" \
    app=terrafloww \
    component=catalog

echo "✅ Secret created successfully!"

# Apply the ConfigMap
echo "📝 Applying catalog ConfigMap..."
kubectl apply -f "$(dirname "$0")/../infra/k8s/catalog-secrets.yaml"

echo "🎉 Catalog secrets and configuration applied successfully!"
echo ""
echo "Next steps:"
echo "1. Rebuild and redeploy the processing engine with: kubectl rollout restart deployment/terrafloww-processing-engine -n $NAMESPACE"
echo "2. Verify the deployment picks up the new environment variables"
echo "3. Test the catalog connectivity"
