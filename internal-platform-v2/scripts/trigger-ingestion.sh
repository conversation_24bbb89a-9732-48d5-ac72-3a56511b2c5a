#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

set -e

# Configuration
REGISTRY="registry.digitalocean.com/terrafloww-dev"
IMAGE_NAME="data-ingestion"
VERSION="${VERSION:-latest}"
FULL_IMAGE="${REGISTRY}/${IMAGE_NAME}:${VERSION}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PLUGIN=""
COLLECTION=""
BBOX=""
DATETIME=""
MAX_ITEMS="10"
BATCH_SIZE="5"
LOG_LEVEL="INFO"
WAIT_FOR_COMPLETION="true"

# Usage function
usage() {
    echo "Usage: $0 <plugin> [collection] [options]"
    echo ""
    echo "Arguments:"
    echo "  plugin      Plugin name (e.g., stac)"
    echo "  collection  Collection name (for STAC plugin, e.g., sentinel-2-l2a)"
    echo ""
    echo "Options:"
    echo "  --bbox=BBOX           Bounding box as 'west,south,east,north'"
    echo "  --datetime=RANGE      Date range as 'YYYY-MM-DD/YYYY-MM-DD'"
    echo "  --max-items=N         Maximum items to process (default: 10)"
    echo "  --batch-size=N        Items per batch (default: 5)"
    echo "  --log-level=LEVEL     Log level (default: INFO)"
    echo "  --no-wait             Don't wait for job completion"
    echo "  --help                Show this help"
    echo ""
    echo "Examples:"
    echo "  # STAC Sentinel-2 ingestion"
    echo "  $0 stac sentinel-2-l2a \\"
    echo "    --bbox='-122.5,37.7,-122.3,37.9' \\"
    echo "    --datetime='2024-06-01/2024-06-25' \\"
    echo "    --max-items=10"
    echo ""
    echo "  # Quick test with minimal data"
    echo "  $0 stac sentinel-2-l2a \\"
    echo "    --bbox='-122.5,37.7,-122.3,37.9' \\"
    echo "    --datetime='2024-06-20/2024-06-21' \\"
    echo "    --max-items=1"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --bbox=*)
            BBOX="${1#*=}"
            shift
            ;;
        --datetime=*)
            DATETIME="${1#*=}"
            shift
            ;;
        --max-items=*)
            MAX_ITEMS="${1#*=}"
            shift
            ;;
        --batch-size=*)
            BATCH_SIZE="${1#*=}"
            shift
            ;;
        --log-level=*)
            LOG_LEVEL="${1#*=}"
            shift
            ;;
        --no-wait)
            WAIT_FOR_COMPLETION="false"
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        -*)
            echo "Unknown option $1"
            usage
            exit 1
            ;;
        *)
            if [[ -z "$PLUGIN" ]]; then
                PLUGIN="$1"
            elif [[ -z "$COLLECTION" ]]; then
                COLLECTION="$1"
            else
                echo "Too many positional arguments"
                usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required arguments
if [[ -z "$PLUGIN" ]]; then
    echo -e "${RED}❌ Plugin name is required${NC}"
    usage
    exit 1
fi

if [[ "$PLUGIN" == "stac" ]]; then
    if [[ -z "$COLLECTION" ]]; then
        echo -e "${RED}❌ Collection is required for STAC plugin${NC}"
        usage
        exit 1
    fi
    
    if [[ -z "$BBOX" ]]; then
        echo -e "${RED}❌ --bbox is required for STAC plugin${NC}"
        usage
        exit 1
    fi
    
    if [[ -z "$DATETIME" ]]; then
        echo -e "${RED}❌ --datetime is required for STAC plugin${NC}"
        usage
        exit 1
    fi
fi

# Generate job name
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
JOB_NAME="data-ingestion-${PLUGIN}-${TIMESTAMP}"

echo -e "${BLUE}🚀 Triggering Data Ingestion${NC}"
echo "============================="
echo "Plugin: $PLUGIN"
if [[ -n "$COLLECTION" ]]; then
    echo "Collection: $COLLECTION"
fi
echo "Job Name: $JOB_NAME"
echo ""

# Build command arguments
ARGS=("$PLUGIN")

if [[ "$PLUGIN" == "stac" ]]; then
    ARGS+=("--collection=$COLLECTION")
    ARGS+=("--bbox=$BBOX")
    ARGS+=("--datetime=$DATETIME")
    ARGS+=("--max-items=$MAX_ITEMS")
    ARGS+=("--batch-size=$BATCH_SIZE")
fi

ARGS+=("--log-level=$LOG_LEVEL")

# Create Kubernetes job
echo -e "${YELLOW}📝 Creating Kubernetes job...${NC}"

kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: ${JOB_NAME}
  namespace: terrafloww-platform
  labels:
    app: data-ingestion
    plugin: ${PLUGIN}
    $(if [[ -n "$COLLECTION" ]]; then echo "collection: ${COLLECTION}"; fi)
    triggered-by: script
spec:
  ttlSecondsAfterFinished: 3600  # Clean up after 1 hour
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: data-ingestion
        plugin: ${PLUGIN}
    spec:
      serviceAccountName: data-ingestion
      restartPolicy: Never
      imagePullSecrets:
      - name: terrafloww-dev
      containers:
      - name: ingestion
        image: ${FULL_IMAGE}
        imagePullPolicy: Always
        command: ["uv", "run", "python", "main.py"]
        args: [$(printf '"%s",' "${ARGS[@]}" | sed 's/,$//')]
        env:
        # S3 Configuration
        - name: STAC_CATALOG_S3_BUCKET
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: bucket
        - name: STAC_CATALOG_S3_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: endpoint
        - name: STAC_CATALOG_S3_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        - name: STAC_CATALOG_S3_PATH_PREFIX
          value: "catalog"
        
        # AWS Credentials
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: access_key_id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: secret_access_key
        - name: AWS_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
EOF

echo -e "${GREEN}✅ Job created: ${JOB_NAME}${NC}"

if [[ "$WAIT_FOR_COMPLETION" == "true" ]]; then
    echo -e "${YELLOW}⏳ Waiting for job completion...${NC}"
    echo "You can monitor progress with: kubectl logs job/${JOB_NAME} -f"
    echo ""
    
    # Wait for job to start
    sleep 5
    
    # Monitor job status
    for i in {1..60}; do  # Wait up to 5 minutes
        STATUS=$(kubectl get job $JOB_NAME -o jsonpath='{.status.conditions[0].type}' 2>/dev/null || echo "")
        
        if [[ "$STATUS" == "Complete" ]]; then
            echo -e "${GREEN}✅ Job completed successfully!${NC}"
            echo ""
            echo -e "${BLUE}📋 Job logs:${NC}"
            kubectl logs job/$JOB_NAME
            exit 0
        elif [[ "$STATUS" == "Failed" ]]; then
            echo -e "${RED}❌ Job failed!${NC}"
            echo ""
            echo -e "${BLUE}📋 Job logs:${NC}"
            kubectl logs job/$JOB_NAME
            exit 1
        fi
        
        # Show progress
        if [[ $((i % 6)) -eq 0 ]]; then  # Every 30 seconds
            echo "Still running... ($((i * 5)) seconds elapsed)"
        fi
        
        sleep 5
    done
    
    echo -e "${YELLOW}⚠️ Job is still running after 5 minutes${NC}"
    echo "Monitor with: kubectl logs job/${JOB_NAME} -f"
    echo "Check status with: kubectl get job ${JOB_NAME}"
else
    echo -e "${BLUE}📖 Job started. Monitor with:${NC}"
    echo "kubectl logs job/${JOB_NAME} -f"
    echo "kubectl get job ${JOB_NAME}"
fi
