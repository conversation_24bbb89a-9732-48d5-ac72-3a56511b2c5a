#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

set -e

# Configuration
REGISTRY="registry.digitalocean.com/terrafloww-dev"
IMAGE_NAME="data-ingestion"
VERSION="${VERSION:-latest}"
FULL_IMAGE="${REGISTRY}/${IMAGE_NAME}:${VERSION}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying Data Ingestion Framework${NC}"
echo "=================================="

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

if ! command -v doctl &> /dev/null; then
    echo -e "${RED}❌ doctl is not installed${NC}"
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f "services/data-ingestion/Dockerfile" ]]; then
    echo -e "${RED}❌ Please run this script from the internal-platform-v2 root directory${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Build Docker image
echo -e "${YELLOW}🔨 Building Docker image...${NC}"
cd services/data-ingestion

# Ensure uv.lock exists
if [[ ! -f "uv.lock" ]]; then
    echo "Creating uv.lock file..."
    uv lock
fi

docker build -t ${FULL_IMAGE} .

echo -e "${GREEN}✅ Docker image built: ${FULL_IMAGE}${NC}"

# Push to registry
echo -e "${YELLOW}📤 Pushing to DigitalOcean Container Registry...${NC}"

# Authenticate with registry
doctl registry login

# Push image
docker push ${FULL_IMAGE}

echo -e "${GREEN}✅ Image pushed to registry${NC}"

# Go back to root directory
cd ../..

# Deploy RBAC
echo -e "${YELLOW}🔐 Deploying RBAC configuration...${NC}"
kubectl apply -f infra/k8s/data-ingestion/rbac.yaml

echo -e "${GREEN}✅ RBAC configuration deployed${NC}"

# Verify secrets exist
echo -e "${YELLOW}🔍 Verifying secrets...${NC}"
if ! kubectl get secret terrafloww-catalog-secrets &> /dev/null; then
    echo -e "${RED}❌ terrafloww-catalog-secrets not found${NC}"
    echo "Please run: kubectl apply -f infra/k8s/catalog-secrets.yaml"
    exit 1
fi

echo -e "${GREEN}✅ Secrets verified${NC}"

# Test deployment with example job
echo -e "${YELLOW}🧪 Testing deployment with example job...${NC}"

# Create test job
kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: data-ingestion-test-$(date +%s)
  namespace: terrafloww-platform
  labels:
    app: data-ingestion
    plugin: stac
    test: true
spec:
  ttlSecondsAfterFinished: 300  # Clean up after 5 minutes
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: data-ingestion
        plugin: stac
    spec:
      serviceAccountName: data-ingestion
      restartPolicy: Never
      containers:
      - name: ingestion
        image: ${FULL_IMAGE}
        imagePullPolicy: Always
        command: ["uv", "run", "python", "main.py"]
        args: ["--list-plugins"]
        env:
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
EOF

echo -e "${GREEN}✅ Test job created${NC}"

# Wait for job to complete
echo -e "${YELLOW}⏳ Waiting for test job to complete...${NC}"
sleep 10

# Check job status
TEST_JOB=$(kubectl get jobs -l test=true --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[-1].metadata.name}')
if [[ -n "$TEST_JOB" ]]; then
    echo "Test job: $TEST_JOB"
    
    # Wait up to 60 seconds for completion
    for i in {1..12}; do
        STATUS=$(kubectl get job $TEST_JOB -o jsonpath='{.status.conditions[0].type}' 2>/dev/null || echo "")
        if [[ "$STATUS" == "Complete" ]]; then
            echo -e "${GREEN}✅ Test job completed successfully${NC}"
            break
        elif [[ "$STATUS" == "Failed" ]]; then
            echo -e "${RED}❌ Test job failed${NC}"
            kubectl logs job/$TEST_JOB
            exit 1
        fi
        echo "Waiting for job completion... ($i/12)"
        sleep 5
    done
    
    # Show logs
    echo -e "${BLUE}📋 Test job logs:${NC}"
    kubectl logs job/$TEST_JOB
else
    echo -e "${YELLOW}⚠️ Could not find test job${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Data Ingestion Framework deployed successfully!${NC}"
echo ""
echo -e "${BLUE}📖 Usage Examples:${NC}"
echo ""
echo "# Trigger STAC ingestion:"
echo "./scripts/trigger-ingestion.sh stac sentinel-2-l2a \\"
echo "  --bbox='-122.5,37.7,-122.3,37.9' \\"
echo "  --datetime='2024-06-01/2024-06-25' \\"
echo "  --max-items=10"
echo ""
echo "# List available plugins:"
echo "kubectl run test-plugins --rm -i --tty --restart=Never \\"
echo "  --image=${FULL_IMAGE} \\"
echo "  --serviceaccount=data-ingestion \\"
echo "  -- python main.py --list-plugins"
echo ""
echo "# Monitor jobs:"
echo "kubectl get jobs -l app=data-ingestion"
echo "kubectl logs job/<job-name>"
