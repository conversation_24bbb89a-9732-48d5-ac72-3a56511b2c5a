# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for AWS S3-based STAC ingestion.
This script tests the updated ingest_ext_stac.py with AWS S3.
"""

import os
import sys
import logging
import subprocess
import base64
import yaml
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_aws_credentials():
    """Set up AWS S3 credentials from the Kubernetes secrets file."""
    logger.info("🔑 Setting up AWS S3 credentials from catalog-secrets.yaml...")

    # Read credentials from the centralized K8s secrets file
    secrets_file = Path(__file__).parent.parent / "infra/k8s/catalog-secrets.yaml"

    if not secrets_file.exists():
        raise FileNotFoundError(f"Secrets file not found: {secrets_file}")

    # Parse the YAML file to extract base64 encoded values

    with open(secrets_file, 'r') as f:
        docs = list(yaml.safe_load_all(f))

    # Find the Secret document
    secret_doc = None
    for doc in docs:
        if doc and doc.get('kind') == 'Secret':
            secret_doc = doc
            break

    if not secret_doc:
        raise ValueError("No Secret document found in catalog-secrets.yaml")

    # Extract base64 encoded values
    data = secret_doc.get('data', {})
    bucket_b64 = data.get('bucket')
    endpoint_b64 = data.get('endpoint')
    region_b64 = data.get('region')
    access_key_b64 = data.get('access_key_id')
    secret_key_b64 = data.get('secret_access_key')

    if not all([bucket_b64, endpoint_b64, region_b64, access_key_b64, secret_key_b64]):
        raise ValueError("Missing required credentials in catalog-secrets.yaml")
    
    # Decode values
    bucket = base64.b64decode(bucket_b64).decode().strip()
    endpoint = base64.b64decode(endpoint_b64).decode().strip()
    region = base64.b64decode(region_b64).decode().strip()
    access_key = base64.b64decode(access_key_b64).decode().strip()
    secret_key = base64.b64decode(secret_key_b64).decode().strip()
    
    # Set up S3 catalog environment variables
    os.environ["STAC_CATALOG_S3_BUCKET"] = bucket
    os.environ["STAC_CATALOG_S3_ENDPOINT"] = endpoint
    os.environ["STAC_CATALOG_S3_REGION"] = region
    os.environ["STAC_CATALOG_S3_PATH_PREFIX"] = "catalog"
    os.environ["AWS_ACCESS_KEY_ID"] = access_key
    os.environ["AWS_SECRET_ACCESS_KEY"] = secret_key
    os.environ["AWS_REGION"] = region
    os.environ["AWS_ENDPOINT_URL"] = endpoint
    
    logger.info(f"✅ AWS S3 configuration:")
    logger.info(f"  Bucket: {bucket}")
    logger.info(f"  Region: {region}")
    logger.info(f"  Endpoint: {endpoint}")
    logger.info(f"  Access Key: {access_key[:8]}...")
    
    return bucket, region, endpoint

def test_aws_s3_connectivity():
    """Test basic AWS S3 connectivity before ingestion."""
    logger.info("🌐 Testing AWS S3 connectivity...")
    
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        # Create S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ["AWS_ACCESS_KEY_ID"],
            aws_secret_access_key=os.environ["AWS_SECRET_ACCESS_KEY"],
            endpoint_url=os.environ["AWS_ENDPOINT_URL"],
            region_name=os.environ["AWS_REGION"]
        )
        
        # Test bucket access
        bucket_name = os.environ["STAC_CATALOG_S3_BUCKET"]
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ Successfully connected to AWS S3 bucket: {bucket_name}")
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            logger.error(f"❌ Bucket {bucket_name} does not exist")
        elif error_code == '403':
            logger.error(f"❌ Access denied to bucket {bucket_name}")
        else:
            logger.error(f"❌ AWS S3 error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to connect to AWS S3: {e}")
        return False

def run_stac_ingestion():
    """Run STAC ingestion with small test dataset to AWS S3."""
    logger.info("🚀 Starting STAC ingestion test with AWS S3...")
    
    # Configuration for larger test
    stac_api = "https://earth-search.aws.element84.com/v1"
    collection = "sentinel-2-l2a"
    bbox = "-122.5,37.7,-122.3,37.9"  # Small area around San Francisco
    datetime_filter = "2024-06-01/2024-06-25"  # Larger 25-day period
    max_items = 10  # Larger test
    
    # Build command - use shell=True to handle negative coordinates properly
    script_path = Path(__file__).parent / "ingest_ext_stac.py"
    cmd_str = f'{sys.executable} "{script_path}" {stac_api} {collection} --bbox="{bbox}" --datetime="{datetime_filter}" --max-items={max_items} --batch-size=1 --limit=5 --log-level=INFO'
    
    logger.info(f"Running command: {cmd_str}")
    
    try:
        # Run the ingestion script
        result = subprocess.run(
            cmd_str,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300,  # 5 minute timeout
            env=os.environ.copy()  # Pass current environment variables
        )
        
        if result.returncode == 0:
            logger.info("✅ STAC ingestion completed successfully!")
            logger.info("Output:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
        else:
            logger.error("❌ STAC ingestion failed!")
            logger.error("STDOUT:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.error(f"  {line}")
            logger.error("STDERR:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    logger.error(f"  {line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        logger.error("❌ STAC ingestion timed out after 5 minutes")
        return False
    except Exception as e:
        logger.error(f"❌ Error running STAC ingestion: {e}")
        return False

def verify_aws_delta_table():
    """Verify that the Delta table was created in AWS S3."""
    logger.info("🔍 Verifying Delta table creation in AWS S3...")
    
    try:
        import boto3
        
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ["AWS_ACCESS_KEY_ID"],
            aws_secret_access_key=os.environ["AWS_SECRET_ACCESS_KEY"],
            endpoint_url=os.environ["AWS_ENDPOINT_URL"],
            region_name=os.environ["AWS_REGION"]
        )
        
        bucket = os.environ["STAC_CATALOG_S3_BUCKET"]
        prefix = f"{os.environ['STAC_CATALOG_S3_PATH_PREFIX']}/ext_stac_datasets/"
        
        # List objects in the Delta table path
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix,
            MaxKeys=20
        )
        
        if 'Contents' in response:
            logger.info(f"✅ Found {len(response['Contents'])} objects in Delta table:")
            for obj in response['Contents'][:10]:  # Show first 10
                logger.info(f"  - {obj['Key']} ({obj['Size']} bytes)")
            return True
        else:
            logger.warning("⚠️ No objects found in Delta table path")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying Delta table: {e}")
        return False

def main():
    """Run the complete AWS S3 STAC ingestion test."""
    logger.info("🧪 Starting AWS S3 STAC ingestion test...")
    
    # Step 1: Set up AWS credentials
    bucket, region, endpoint = setup_aws_credentials()
    
    # Step 2: Test AWS S3 connectivity
    if not test_aws_s3_connectivity():
        logger.error("❌ AWS S3 connectivity test failed")
        return False
    
    # Step 3: Run STAC ingestion
    if not run_stac_ingestion():
        logger.error("❌ STAC ingestion failed")
        return False
    
    # Step 4: Verify Delta table
    if not verify_aws_delta_table():
        logger.error("❌ Delta table verification failed")
        return False
    
    logger.info("🎉 All tests passed! AWS S3 STAC ingestion is working correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
