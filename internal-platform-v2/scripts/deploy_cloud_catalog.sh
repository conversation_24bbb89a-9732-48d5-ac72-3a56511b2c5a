#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Complete deployment script for cloud-native STAC catalog on DigitalOcean Spaces

set -e

# Configuration
NAMESPACE="terrafloww-platform"
DEPLOYMENT_NAME="terrafloww-processing-engine"
SECRET_NAME="terrafloww-catalog-secrets"
CREDENTIALS_FILE="${CREDENTIALS_FILE:-/home/<USER>/Work/platform-build/do_key.env}"

echo "🚀 Deploying Cloud-Native STAC Catalog on DigitalOcean Spaces"
echo "=============================================================="

# Change to script directory
cd "$(dirname "$0")"

# Step 1: Validate prerequisites
echo "📋 Step 1: Validating prerequisites..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl not found. Please install kubectl."
    exit 1
fi

# Check if docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ docker not found. Please install Docker."
    exit 1
fi

# Check if credentials file exists
if [[ ! -f "$CREDENTIALS_FILE" ]]; then
    echo "❌ Credentials file not found: $CREDENTIALS_FILE"
    echo "Please create the file with your DigitalOcean Spaces credentials."
    exit 1
fi

echo "✅ Prerequisites validated"

# Step 2: Test DO Spaces connectivity
echo ""
echo "🌐 Step 2: Testing DigitalOcean Spaces connectivity..."
if python test_do_spaces_connectivity.py; then
    echo "✅ DO Spaces connectivity test passed"
else
    echo "❌ DO Spaces connectivity test failed"
    echo "Please check your credentials and try again."
    exit 1
fi

# Step 3: Set up Kubernetes secrets
echo ""
echo "🔐 Step 3: Setting up Kubernetes secrets..."
if ./setup_catalog_secrets.sh; then
    echo "✅ Kubernetes secrets created successfully"
else
    echo "❌ Failed to create Kubernetes secrets"
    exit 1
fi

# Step 4: Rebuild and deploy processing engine
echo ""
echo "🏗️ Step 4: Rebuilding and deploying processing engine..."
if ./rebuild_and_deploy_processing_engine.sh; then
    echo "✅ Processing engine deployed successfully"
else
    echo "❌ Failed to deploy processing engine"
    exit 1
fi

# Step 5: Test catalog connectivity in Kubernetes
echo ""
echo "🧪 Step 5: Testing catalog connectivity in Kubernetes..."

# Wait for pod to be ready
echo "⏳ Waiting for processing engine pod to be ready..."
kubectl wait --for=condition=ready pod -l app=terrafloww,component=processing-engine -n "$NAMESPACE" --timeout=300s

# Get pod name
POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l app=terrafloww,component=processing-engine -o jsonpath='{.items[0].metadata.name}')

if [[ -n "$POD_NAME" ]]; then
    echo "📋 Testing catalog connectivity in pod: $POD_NAME"
    
    # Copy test script to pod
    kubectl cp test_k8s_catalog_connectivity.py "$NAMESPACE/$POD_NAME:/tmp/test_catalog.py"
    
    # Run test in pod
    if kubectl exec -n "$NAMESPACE" "$POD_NAME" -- python /tmp/test_catalog.py; then
        echo "✅ Kubernetes catalog connectivity test passed"
    else
        echo "❌ Kubernetes catalog connectivity test failed"
        echo "Check the pod logs for more details:"
        echo "kubectl logs -n $NAMESPACE $POD_NAME"
        exit 1
    fi
else
    echo "❌ No processing engine pod found"
    exit 1
fi

# Step 6: Create initial STAC catalog data
echo ""
echo "📊 Step 6: Creating initial STAC catalog data..."

# Source credentials for local ingestion test
source "$CREDENTIALS_FILE"

# Set up environment variables for S3
export STAC_CATALOG_S3_BUCKET="dev-datasets"
export STAC_CATALOG_S3_ENDPOINT="$do_space_endpoint"
export STAC_CATALOG_S3_REGION="$do_region"
export STAC_CATALOG_S3_PATH_PREFIX="catalog"
export DO_ACCESS_KEY_ID="$do_access_key_id"
export DO_SECRET_ACCESS_KEY="$do_secret_access_key"
export DO_REGION="$do_region"
export DO_SPACE_ENDPOINT="$do_space_endpoint"

echo "🔄 Running S3 STAC ingestion test..."
if python test_s3_stac_ingestion.py; then
    echo "✅ S3 STAC ingestion test passed"
else
    echo "❌ S3 STAC ingestion test failed"
    echo "You can run it manually later with: python test_s3_stac_ingestion.py"
fi

# Step 7: Final validation
echo ""
echo "🔍 Step 7: Final validation..."

# Check if Delta table exists in S3
echo "Checking Delta table in S3..."
python -c "
import boto3
import os
try:
    s3 = boto3.client(
        's3',
        aws_access_key_id=os.environ['DO_ACCESS_KEY_ID'],
        aws_secret_access_key=os.environ['DO_SECRET_ACCESS_KEY'],
        endpoint_url=os.environ['DO_SPACE_ENDPOINT'],
        region_name=os.environ['DO_REGION']
    )
    response = s3.list_objects_v2(
        Bucket='dev-datasets',
        Prefix='catalog/ext_stac_datasets/',
        MaxKeys=5
    )
    if 'Contents' in response:
        print(f'✅ Found {len(response[\"Contents\"])} objects in Delta table')
        for obj in response['Contents']:
            print(f'  - {obj[\"Key\"]} ({obj[\"Size\"]} bytes)')
    else:
        print('⚠️ No objects found in Delta table (this is OK for initial setup)')
except Exception as e:
    print(f'❌ Error checking Delta table: {e}')
"

echo ""
echo "🎉 Cloud-Native STAC Catalog Deployment Complete!"
echo "=================================================="
echo ""
echo "📋 Summary:"
echo "  ✅ DigitalOcean Spaces connectivity verified"
echo "  ✅ Kubernetes secrets configured"
echo "  ✅ Processing engine deployed with S3 support"
echo "  ✅ Catalog connectivity tested in Kubernetes"
echo "  ✅ Initial STAC data ingestion tested"
echo ""
echo "🔧 Next steps:"
echo "  1. Run end-to-end workflow tests"
echo "  2. Populate catalog with more STAC data as needed"
echo "  3. Monitor processing engine logs for any issues"
echo ""
echo "📖 Useful commands:"
echo "  - Check pod status: kubectl get pods -n $NAMESPACE"
echo "  - View pod logs: kubectl logs -n $NAMESPACE $POD_NAME"
echo "  - Test catalog: kubectl exec -n $NAMESPACE $POD_NAME -- python /tmp/test_catalog.py"
echo "  - Run STAC ingestion: python ingest_ext_stac.py <stac-api> <collection>"
