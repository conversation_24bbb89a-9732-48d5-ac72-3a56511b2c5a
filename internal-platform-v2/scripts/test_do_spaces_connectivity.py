# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script to verify DigitalOcean Spaces connectivity using boto3.
This script tests basic operations like listing, uploading, and downloading.
"""

import os
import sys
import boto3
import logging
from pathlib import Path
from botocore.exceptions import ClientError, NoCredentialsError
from dotenv import load_dotenv
from botocore.client import Config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_do_credentials(env_file_path: str) -> dict:
    """Load DigitalOcean credentials from environment file using python-dotenv."""
    if not os.path.exists(env_file_path):
        raise FileNotFoundError(f"Credentials file not found: {env_file_path}")

    # Load environment variables from file
    load_dotenv(env_file_path)

    # Extract the required credentials
    credentials = {
        'do_access_key_id': os.getenv('do_access_key_id'),
        'do_secret_access_key': os.getenv('do_secret_access_key'),
        'do_space_endpoint': os.getenv('do_space_endpoint'),
        'do_region': os.getenv('do_region')
    }

    # Check for missing credentials
    missing = [k for k, v in credentials.items() if not v]
    if missing:
        raise ValueError(f"Missing credentials in {env_file_path}: {missing}")

    logger.info(f"Loaded credentials from {env_file_path}")
    return credentials

def create_s3_client(credentials: dict) -> boto3.client:
    """Create boto3 S3 client configured for DigitalOcean Spaces."""
    
    # Extract credentials
    access_key = credentials.get('do_access_key_id')
    secret_key = credentials.get('do_secret_access_key')
    endpoint_url = credentials.get('do_space_endpoint')
    region = credentials.get('do_region')
    
    if not all([access_key, secret_key, endpoint_url, region]):
        raise ValueError("Missing required credentials: do_access_key_id, do_secret_access_key, do_space_endpoint, do_region")
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        endpoint_url=endpoint_url,
        region_name=region,
        config=Config(s3={'addressing_style': 'path'})
    )
    
    logger.info(f"Created S3 client for endpoint: {endpoint_url}")
    return s3_client

def test_bucket_access(s3_client: boto3.client, bucket_name: str) -> bool:
    """Test basic bucket access."""
    try:
        # Test bucket exists and we can access it
        response = s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ Successfully accessed bucket: {bucket_name}")
        return True
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            logger.error(f"❌ Bucket {bucket_name} does not exist")
        elif error_code == '403':
            logger.error(f"❌ Access denied to bucket {bucket_name}")
        else:
            logger.error(f"❌ Error accessing bucket {bucket_name}: {e}")
        return False

def test_list_objects(s3_client: boto3.client, bucket_name: str) -> bool:
    """Test listing objects in bucket."""
    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=10)
        
        if 'Contents' in response:
            logger.info(f"✅ Bucket contains {response.get('KeyCount', 0)} objects (showing first 10)")
            for obj in response['Contents'][:5]:  # Show first 5
                logger.info(f"   - {obj['Key']} ({obj['Size']} bytes)")
        else:
            logger.info(f"✅ Bucket {bucket_name} is empty")
        return True
    except ClientError as e:
        logger.error(f"❌ Error listing objects in {bucket_name}: {e}")
        return False

def test_upload_download(s3_client: boto3.client, bucket_name: str) -> bool:
    """Test upload and download operations."""
    test_key = "test/connectivity_test.txt"
    test_content = "Hello from Terrafloww platform! This is a connectivity test."
    
    try:
        # Test upload
        s3_client.put_object(
            Bucket=bucket_name,
            Key=test_key,
            Body=test_content.encode('utf-8'),
            ContentType='text/plain'
        )
        logger.info(f"✅ Successfully uploaded test file: {test_key}")
        
        # Test download
        response = s3_client.get_object(Bucket=bucket_name, Key=test_key)
        downloaded_content = response['Body'].read().decode('utf-8')
        
        if downloaded_content == test_content:
            logger.info(f"✅ Successfully downloaded and verified test file")
        else:
            logger.error(f"❌ Downloaded content doesn't match uploaded content")
            return False
        
        # # Clean up test file
        # s3_client.delete_object(Bucket=bucket_name, Key=test_key)
        # logger.info(f"✅ Successfully deleted test file")
        
        return True
    except ClientError as e:
        logger.error(f"❌ Error during upload/download test: {e}")
        return False

def list_all_buckets(s3_client: boto3.client) -> bool:
    """List all available buckets/spaces."""
    try:
        response = s3_client.list_buckets()
        buckets = response.get('Buckets', [])

        if buckets:
            logger.info(f"📦 Found {len(buckets)} bucket(s):")
            for bucket in buckets:
                logger.info(f"   - {bucket['Name']} (created: {bucket['CreationDate']})")
        else:
            logger.info("📦 No buckets found")
        return True
    except ClientError as e:
        logger.error(f"❌ Error listing buckets: {e}")
        return False

def create_bucket_if_not_exists(s3_client: boto3.client, bucket_name: str, region: str) -> bool:
    """Create bucket if it doesn't exist."""
    try:
        # Check if bucket exists
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ Bucket {bucket_name} already exists")
        return True
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            # Bucket doesn't exist, create it
            try:
                s3_client.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={'LocationConstraint': region}
                )
                logger.info(f"✅ Successfully created bucket: {bucket_name}")
                return True
            except ClientError as create_error:
                logger.error(f"❌ Error creating bucket {bucket_name}: {create_error}")
                return False
        else:
            logger.error(f"❌ Error checking bucket {bucket_name}: {e}")
            return False

def main():
    """Main test function."""
    # Configuration
    env_file_path = "/home/<USER>/Work/platform-build/do_key.env"
    bucket_name = "dev-datasets"

    logger.info("🚀 Starting DigitalOcean Spaces connectivity test...")

    try:
        # Load credentials
        credentials = load_do_credentials(env_file_path)

        # Create S3 client
        s3_client = create_s3_client(credentials)

        # First, list all available buckets
        logger.info("\n📦 Checking available buckets...")
        list_all_buckets(s3_client)

        # Create bucket if it doesn't exist
        logger.info(f"\n🏗️ Ensuring bucket exists: {bucket_name}")
        if not create_bucket_if_not_exists(s3_client, bucket_name, credentials['do_region']):
            logger.error("❌ Failed to create/access bucket. Aborting tests.")
            return False

        # Run tests
        tests_passed = 0
        total_tests = 3

        logger.info(f"\n📋 Running connectivity tests for bucket: {bucket_name}")

        # Test 1: Bucket access
        if test_bucket_access(s3_client, bucket_name):
            tests_passed += 1

        # Test 2: List objects
        if test_list_objects(s3_client, bucket_name):
            tests_passed += 1

        # Test 3: Upload/download
        if test_upload_download(s3_client, bucket_name):
            tests_passed += 1
        
        # Summary
        logger.info(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            logger.info("🎉 All tests passed! DigitalOcean Spaces connectivity is working.")
            return True
        else:
            logger.error("❌ Some tests failed. Please check the configuration.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
