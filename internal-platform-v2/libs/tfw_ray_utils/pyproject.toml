[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tfw-ray-utils"
version = "0.1.0"
description = "Low-level, generic Ray utilities for Terrafloww Platform"
readme = "README.md"
requires-python = ">=3.10"
license = { text = "Proprietary" }
authors = [
    { name = "Terrafloww Team" },
]
dependencies = [
    "ray>=2.9.0",
    "pyarrow>=14.0.0",
    "shapely>=2.0.0",
    "httpx[http2]>=0.25.0",
    "numpy>=1.20.0",
    "rasterio>=1.3.0",
    "affine>=2.3.0",
    "pyproj>=3.0.0",
    "imagecodecs>=2023.0.0",
    "cachetools>=5.0.0",
]

[project.urls]
"Homepage" = "https://github.com/terrafloww/platform"
"Bug Tracker" = "https://github.com/terrafloww/platform/issues"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
