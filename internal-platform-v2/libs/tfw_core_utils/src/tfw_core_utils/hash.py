"""Hash utility functions for Terrafloww Platform."""

import json
import hashlib
from typing import Any, Optional


def create_canonical_hash(obj: Any) -> Optional[str]:
    """
    Creates a deterministic hash from any JSON-serializable object.
    Returns None if the object is None or empty.
    
    This is used to generate consistent hashes for parameters and source artifacts
    to enable content-based lookups.
    
    Args:
        obj: Any JSON-serializable object to hash
        
    Returns:
        A hex string SHA-256 hash of the canonicalized JSON representation,
        or None if the input is None or empty
    """
    if obj is None or (isinstance(obj, (dict, list)) and len(obj) == 0):
        return None
        
    # Sort keys and use a stable serialization format
    serialized = json.dumps(obj, sort_keys=True, separators=(',', ':'))
    
    # Create a SHA-256 hash
    hash_obj = hashlib.sha256(serialized.encode('utf-8'))
    return hash_obj.hexdigest()
