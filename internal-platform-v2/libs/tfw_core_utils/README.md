# Terrafloww Core Utilities

Core utility functions used across the Terrafloww platform.

## Features

- `create_canonical_hash`: Generates deterministic hashes for JSON-serializable objects
- Additional utilities will be added as needed

## Usage

```python
from tfw_core_utils import create_canonical_hash

# Generate a hash for any JSON-serializable object
params = {"model": "resnet50", "batch_size": 32}
hash_value = create_canonical_hash(params)
```

This library is designed to be lightweight with minimal dependencies.
