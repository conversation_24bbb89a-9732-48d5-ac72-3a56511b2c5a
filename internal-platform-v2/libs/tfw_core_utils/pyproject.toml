[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "tfw-core-utils"
version = "0.1.0"
description = "Core utilities for Terrafloww platform."
readme = "README.md"
requires-python = ">=3.10"
license = { text = "Proprietary" }
authors = [
    { name = "Terrafloww Team" },
]
dependencies = [
    # No external dependencies beyond Python standard library
]

[project.urls]
"Homepage" = "https://github.com/terrafloww/platform"
"Bug Tracker" = "https://github.com/terrafloww/platform/issues"

[tool.hatch.build.targets.wheel]
packages = ["src/tfw_core_utils"]
