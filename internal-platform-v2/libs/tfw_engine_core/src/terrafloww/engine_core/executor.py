"""
Very thin adapter that turns a LogicalPlan (list[PlanStep]) into actual
work for the existing Ray `WorkflowExecutor` (runtime_ray.ray_executor).

Until the Ray‑side public API is stabilised we:
1.  Translate the LogicalPlan to *WorkflowPlan* protobuf (collection + apply funcs).
2.  Invoke `WorkflowExecutor()` (no‑arg ctor) and run it synchronously via
    `execute_ray_workflow`.
3.  Return an **empty Arrow table** placeholder.  (Ray workflow already
    writes an Arrow IPC file; once the executor returns a Table we will
    surface it here.)
"""
from __future__ import annotations

import asyncio, uuid
from typing import List

import pyarrow as pa
from terrafloww.processing_engine.v1 import processing_engine_pb2

from .runtime_ray.ray_executor import WorkflowExecutor
from terrafloww.dsl.translator import PlanStep  # type: ignore
import os
import pyarrow as pa
import logging

logger = logging.getLogger(__name__)