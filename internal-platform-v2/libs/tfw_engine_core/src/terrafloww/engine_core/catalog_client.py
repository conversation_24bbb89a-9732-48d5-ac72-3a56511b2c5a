# SPDX-FileCopyrightText: Terrafloww Labs, 2025

import os, sys
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
import datetime
import httpx
import json
import pytz  # ensure in dependencies
from abc import ABC, abstractmethod

import pyarrow as pa
import pyarrow.compute as pc
from shapely.geometry import shape
import deltalake
from deltalake import DeltaTable

# S3/Cloud storage imports
try:
    import boto3
    import s3fs
    from botocore.exceptions import ClientError, NoCredentialsError
    S3_AVAILABLE = True
except ImportError:
    S3_AVAILABLE = False

# Import band utilities
from terrafloww.engine_core.bands import (
    get_band_id,
    get_common_name,
    get_band_info,
    get_bands_in_group,
    BAND_ALIASES as SENTINEL2_BAND_ALIASES,
    RED, GREEN, BLUE, NIR, SWIR_1, SWIR_2
)

# Storage backend configuration classes
class StorageConfig:
    """Base configuration for storage backends."""

    def __init__(self, storage_type: str = "local"):
        self.storage_type = storage_type

class S3Config(StorageConfig):
    """Configuration for S3-compatible storage backends."""

    def __init__(
        self,
        bucket: str,
        region: str,
        endpoint_url: Optional[str] = None,
        access_key_id: Optional[str] = None,
        secret_access_key: Optional[str] = None,
        path_prefix: str = ""
    ):
        super().__init__("s3")
        self.bucket = bucket
        self.region = region
        self.endpoint_url = endpoint_url
        self.access_key_id = access_key_id or os.environ.get("AWS_ACCESS_KEY_ID")
        self.secret_access_key = secret_access_key or os.environ.get("AWS_SECRET_ACCESS_KEY")
        self.path_prefix = path_prefix.strip("/")

        # For DigitalOcean Spaces, use environment variables with DO prefix
        if not self.access_key_id:
            self.access_key_id = os.environ.get("DO_ACCESS_KEY_ID")
        if not self.secret_access_key:
            self.secret_access_key = os.environ.get("DO_SECRET_ACCESS_KEY")
        if not self.endpoint_url:
            self.endpoint_url = os.environ.get("DO_SPACE_ENDPOINT")
        if not self.region:
            self.region = os.environ.get("DO_REGION", "nyc3")

class StorageBackend(ABC):
    """Abstract base class for storage backends."""

    @abstractmethod
    def get_delta_table_path(self, table_name: str) -> str:
        """Get the full path for a Delta table."""
        pass

    @abstractmethod
    def create_delta_table(self, table_path: str) -> DeltaTable:
        """Create or load a Delta table."""
        pass

class LocalStorageBackend(StorageBackend):
    """Local file system storage backend."""

    def __init__(self, base_path: str):
        self.base_path = base_path

    def get_delta_table_path(self, table_name: str) -> str:
        return os.path.join(self.base_path, table_name)

    def create_delta_table(self, table_path: str) -> DeltaTable:
        return DeltaTable(table_path)

class S3StorageBackend(StorageBackend):
    """S3-compatible storage backend with error handling and retry logic."""

    def __init__(self, config: S3Config):
        if not S3_AVAILABLE:
            raise ImportError("S3 dependencies not available. Install boto3 and s3fs.")

        self.config = config
        self._validate_config()
        self._setup_s3_client()

    def _validate_config(self):
        """Validate S3 configuration."""
        required_fields = ["bucket", "access_key_id", "secret_access_key"]
        missing = [field for field in required_fields if not getattr(self.config, field)]
        if missing:
            raise ValueError(f"Missing required S3 configuration: {missing}")

    def _setup_s3_client(self):
        """Set up S3 client with proper configuration."""
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=self.config.access_key_id,
                aws_secret_access_key=self.config.secret_access_key,
                endpoint_url=self.config.endpoint_url,
                region_name=self.config.region
            )

            # Test connection
            self.s3_client.head_bucket(Bucket=self.config.bucket)
            logger.info(f"Successfully connected to S3 bucket: {self.config.bucket}")

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.error(f"S3 bucket {self.config.bucket} does not exist")
            elif error_code == '403':
                logger.error(f"Access denied to S3 bucket {self.config.bucket}")
            else:
                logger.error(f"Error connecting to S3: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to setup S3 client: {e}")
            raise

    def get_delta_table_path(self, table_name: str) -> str:
        """Get S3 path for Delta table."""
        if self.config.path_prefix:
            return f"s3://{self.config.bucket}/{self.config.path_prefix}/{table_name}"
        else:
            return f"s3://{self.config.bucket}/{table_name}"

    def create_delta_table(self, table_path: str) -> DeltaTable:
        """Create or load Delta table from S3."""
        try:
            # Configure storage options for Delta Lake
            storage_options = {
                "AWS_ACCESS_KEY_ID": self.config.access_key_id,
                "AWS_SECRET_ACCESS_KEY": self.config.secret_access_key,
                "AWS_REGION": self.config.region,
            }

            if self.config.endpoint_url:
                storage_options["AWS_ENDPOINT_URL"] = self.config.endpoint_url
                storage_options["AWS_S3_ALLOW_UNSAFE_RENAME"] = "true"

            return DeltaTable(table_path, storage_options=storage_options)

        except Exception as e:
            logger.error(f"Error creating/loading Delta table from S3: {e}")
            raise

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Band name mappings for different collections
BAND_NAME_MAPPINGS = {
    "sentinel-2-l2a": {
        # Standard Sentinel-2 bands with all known aliases
        "coastal_aerosol": ["B01", "coastal"],
        "blue": ["B02", "B2", "blue"],
        "green": ["B03", "B3", "green"],
        "red": ["B04", "B4", "red"],
        "red_edge_1": ["B05", "B5", "rededge1"],
        "red_edge_2": ["B06", "B6", "rededge2"],
        "red_edge_3": ["B07", "B7", "rededge3"],
        "nir": ["B08", "B8", "nir", "nir08"],
        "nir_narrow": ["B8A", "nir09"],
        "water_vapor": ["B09"],
        "swir_1": ["B11", "swir16"],
        "swir_2": ["B12", "swir22"],
        "scene_classification": ["SCL"],
        "aerosol_optical_thickness": ["AOT"],
        "water_vapour": ["WVP"]
    },
    "landsat-c2l2-sr": {
        "coastal_aerosol": ["SR_B1"],
        "blue": ["SR_B2"],
        "green": ["SR_B3"],
        "red": ["SR_B4"],
        "nir": ["SR_B5"],
        "swir_1": ["SR_B6"],
        "swir_2": ["SR_B7"],
        "qa_pixel": ["QA_PIXEL"],
        "qa_radsat": ["QA_RADSAT"]
    }
}

def get_band_aliases(collection: str, band_name: str) -> List[str]:
    """
    Get all possible aliases for a band name in a collection.
    
    Args:
        collection: The collection name
        band_name: The band name to get aliases for
        
    Returns:
        List of aliases for the band
    """
    logger.info(f"Resolving aliases for band: {band_name} in collection: {collection}")
    
    try:
        # First try to get the standard band ID (e.g., 'red' -> 'B04')
        band_id = get_band_id(band_name)
        logger.info(f"Standardized band name: {band_name} -> {band_id}")
        
        # Get all possible aliases for this band
        aliases = [band_id]
        
        # Add common name if different from band ID
        common_name = get_common_name(band_id)
        if common_name and common_name != band_id:
            logger.info(f"Adding common name: {common_name}")
            aliases.append(common_name)
            
        # Add any additional aliases from the collection-specific mappings
        if collection in BAND_NAME_MAPPINGS:
            logger.info(f"Checking collection-specific mappings for: {collection}")
            for key, values in BAND_NAME_MAPPINGS[collection].items():
                if key == band_id or key == common_name or band_id in values:
                    logger.info(f"Adding aliases from mapping: {values}")
                    aliases.extend(values)
        
        # Ensure we don't have duplicates and preserve the original band name
        unique_aliases = list(dict.fromkeys([band_name] + aliases))
        logger.info(f"Final aliases for {band_name} in {collection}: {unique_aliases}")
        return unique_aliases
        
    except ValueError as e:
        logger.warning(f"Could not standardize band name '{band_name}': {e}")
        logger.info("Falling back to collection-specific mappings")
        
        # Fall back to collection-specific mappings
        if collection in BAND_NAME_MAPPINGS:
            collection_mappings = BAND_NAME_MAPPINGS[collection]
            logger.info(f"Collection mappings: {collection_mappings}")
            
            # Check if the band name is a key in the collection mappings
            if band_name in collection_mappings:
                logger.info(f"Found direct mapping for {band_name}: {collection_mappings[band_name]}")
                return collection_mappings[band_name]
                
            # Check if the band name is a value in any of the mapping lists
            for key, aliases in collection_mappings.items():
                if band_name in aliases:
                    logger.info(f"Found {band_name} in mapping for {key}: {aliases}")
                    return aliases
        
        # If no mapping found, log a warning and return the band name as a single-item list
        logger.warning(f"No mapping found for band: {band_name} in collection: {collection}")
        return [band_name]

class CatalogClient:
    """
    Client for querying the external STAC catalog.
    Uses Delta Lake for efficient querying with support for multiple storage backends.
    """

    def __init__(
        self,
        catalog_path: Optional[str] = None,
        storage_backend: Optional[StorageBackend] = None,
        storage_config: Optional[StorageConfig] = None
    ):
        """
        Initialize the catalog client.

        Args:
            catalog_path: Path to the catalog Delta table (legacy, for backward compatibility)
            storage_backend: Storage backend instance
            storage_config: Storage configuration (will create appropriate backend)
        """
        # Determine storage backend
        if storage_backend:
            self.storage_backend = storage_backend
        elif storage_config:
            self.storage_backend = self._create_storage_backend(storage_config)
        else:
            # Legacy mode: use catalog_path or environment variable
            legacy_path = catalog_path or os.environ.get(
                "STAC_CATALOG_PATH",
                "/tmp/platform_delta_tables/ext_stac_datasets"
            )

            # Check if it's an S3 path
            if legacy_path.startswith("s3://"):
                # Try to create S3 backend from environment variables
                try:
                    s3_config = self._create_s3_config_from_env(legacy_path)
                    self.storage_backend = S3StorageBackend(s3_config)
                    logger.info(f"Created S3 storage backend from legacy path: {legacy_path}")
                except Exception as e:
                    logger.error(f"Failed to create S3 backend from legacy path: {e}")
                    raise
            else:
                # Use local storage backend
                base_path = os.path.dirname(legacy_path)
                table_name = os.path.basename(legacy_path)
                self.storage_backend = LocalStorageBackend(base_path)
                self.table_name = table_name
                logger.info(f"Created local storage backend: {base_path}")

        # Set default table name if not set
        if not hasattr(self, 'table_name'):
            self.table_name = "ext_stac_datasets"

    def _create_storage_backend(self, config: StorageConfig) -> StorageBackend:
        """Create storage backend from configuration."""
        if isinstance(config, S3Config):
            return S3StorageBackend(config)
        else:
            raise ValueError(f"Unsupported storage config type: {type(config)}")

    def _create_s3_config_from_env(self, s3_path: str) -> S3Config:
        """Create S3 config from environment variables and S3 path."""
        # Parse S3 path: s3://bucket/path/to/table
        path_parts = s3_path.replace("s3://", "").split("/")
        bucket = path_parts[0]
        path_prefix = "/".join(path_parts[1:-1]) if len(path_parts) > 2 else ""

        return S3Config(
            bucket=bucket,
            region=os.environ.get("AWS_REGION", os.environ.get("DO_REGION", "nyc3")),
            endpoint_url=os.environ.get("AWS_ENDPOINT_URL", os.environ.get("DO_SPACE_ENDPOINT")),
            path_prefix=path_prefix
        )

    @property
    def catalog_path(self) -> str:
        """Get the full catalog path (for backward compatibility)."""
        return self.storage_backend.get_delta_table_path(self.table_name)
    
    async def query_assets(
        self, 
        collection: str,
        aoi: Optional[Any] = None,  # Shapely geometry
        datetime_range: Optional[Tuple[str, str]] = None,
        bands: Optional[List[str]] = None,
        property_filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> pa.Table:
        """
        Query the catalog for assets matching criteria.
        
        Args:
            collection: Collection name
            aoi: Area of interest (Shapely geometry)
            datetime_range: Datetime range (start, end)
            bands: List of bands to load
            property_filters: Additional property filters
            limit: Maximum number of assets to return
            
        Returns:
            PyArrow Table of matching assets
        """
        logger.info(f"Querying catalog for collection: {collection}")
        
        # Build filters
        filters = [
            ("collection", "=", collection)
        ]
        
        # Add datetime filter if provided
        if datetime_range:
            start, end = datetime_range
            # start_date = datetime.datetime.fromisoformat(start.replace('Z', '+00:00'))
            # end_date = datetime.datetime.fromisoformat(end.replace('Z', '+00:00'))

            # parse as naive then attach UTC tz
            _naive_start = datetime.datetime.fromisoformat(start)
            _naive_end   = datetime.datetime.fromisoformat(end)
            start_date = _naive_start.replace(tzinfo=pytz.UTC)
            end_date   = _naive_end.replace(tzinfo=pytz.UTC)


            # Add filters for year and month partitions
            if start_date.year == end_date.year:
                filters.append(("year", "=", start_date.year))
            else:
                # Spanning multiple years, don't filter on year
                # (This could be optimized for multi-year ranges)
                pass
            
            # Convert to Arrow timestamp for filtering
            filters.append(("datetime", ">=", start_date))
            filters.append(("datetime", "<=", end_date))
        
        # Spatial filter - in production would use more sophisticated indexing
        # For MVP, perform spatial filtering after loading the data
        
        try:
            # Load the Delta table using storage backend
            delta_table = self.storage_backend.create_delta_table(
                self.storage_backend.get_delta_table_path(self.table_name)
            )

            # Query with filters
            assets = delta_table.to_pyarrow_table(filters=filters)
            
            # Apply additional filters (property filters)
            if property_filters:
                # Convert property filters to PyArrow compute expressions
                for prop, value in property_filters.items():
                    if prop == 'cloud_cover' and isinstance(value, (int, float)):
                        assets = assets.filter(pc.field('cloud_cover') <= value)
                    elif prop in assets.column_names:
                        # Direct property filter
                        assets = assets.filter(pc.field(prop) == value)
                    else:
                        # Try to find in JSON properties
                        # For MVP, skip complex property filters
                        logger.warning(f"Property filter not applied: {prop}={value}")
            
            # Apply spatial filter if provided
            if aoi:
                # Simplified spatial filtering for MVP
                # Ideally should use proper spatial indexing
                
                # Check for intersection with bbox
                filtered_indices = []
                
                for i in range(len(assets)):
                    # Get bbox
                    minx = assets['bbox_minx'][i].as_py()
                    miny = assets['bbox_miny'][i].as_py()
                    maxx = assets['bbox_maxx'][i].as_py()
                    maxy = assets['bbox_maxy'][i].as_py()
                    
                    # Check for intersection
                    # Simple bbox check - in production use proper spatial indexing
                    if minx <= aoi.bounds[2] and maxx >= aoi.bounds[0] and \
                       miny <= aoi.bounds[3] and maxy >= aoi.bounds[1]:
                        filtered_indices.append(i)
                
                if filtered_indices:
                    assets = assets.take(filtered_indices)
                else:
                    # No intersection, return empty table
                    logger.warning("No assets intersect with the AOI")
                    return pa.Table.from_pylist([], schema=assets.schema)
            
            # Log available bands in the catalog for debugging
            if len(assets) > 0:
                available_bands = list(set(assets['cog_key'].to_pylist()))
                logger.info(f"Available bands in catalog: {available_bands}")
            else:
                logger.warning("No assets found in catalog")
                return pa.Table.from_pylist([], schema=assets.schema)
            
            # Filter by band if provided
            if bands:
                logger.info(f"Filtering assets for bands: {bands}")
                
                # First, get all possible band aliases for the requested bands
                band_aliases_map = {}
                all_band_aliases = set()
                
                # Expand each band to all its possible aliases
                for band in bands:
                    # Get all aliases for this band
                    aliases = get_band_aliases(collection, band)
                    band_aliases_map[band] = aliases
                    all_band_aliases.update(aliases)
                    
                    # Log the resolution
                    logger.info(f"Band resolution: '{band}' -> {aliases}")
                
                # Also include the original band names in the search
                all_band_aliases.update(bands)
                
                logger.info(f"Looking for these band aliases in catalog: {sorted(list(all_band_aliases))}")
                
                # Get all unique band names in the catalog for debugging
                available_bands = list(set(assets['cog_key'].to_pylist()))
                logger.info(f"Available bands in catalog: {sorted(available_bands)}")
                
                # Get indices of assets that match any of the band aliases
                filtered_indices = []
                for i in range(len(assets)):
                    cog_key = assets['cog_key'][i].as_py()
                    asset_band_name = assets['band_name'][i].as_py() if 'band_name' in assets.column_names else None
                    
                    # Check if either the cog_key or band_name matches any of our aliases
                    is_match = False
                    match_reason = ""
                    
                    if cog_key in all_band_aliases:
                        is_match = True
                        match_reason = f"cog_key matches {cog_key}"
                    elif asset_band_name and asset_band_name in all_band_aliases:
                        is_match = True
                        match_reason = f"band_name matches {asset_band_name}"
                    
                    if is_match:
                        filtered_indices.append(i)
                        logger.debug(f"Found matching band: {cog_key} (name: {asset_band_name}) - {match_reason}")
                
                logger.info(f"Found {len(filtered_indices)} assets matching the requested bands")
                
                if filtered_indices:
                    assets = assets.take(filtered_indices)
                    logger.info(f"Successfully filtered to {len(assets)} assets matching the requested bands")
                else:
                    available_bands = list(set(assets['cog_key'].to_pylist()))
                    logger.error("No requested bands found in catalog. This is likely a configuration issue.")
                    logger.error(f"Available bands in catalog: {sorted(available_bands)}")
                    logger.error(f"Requested bands (after alias expansion): {sorted(list(all_band_aliases))}")
                    
                    # Check for potential case sensitivity issues
                    lower_available = set(b.lower() for b in available_bands)
                    lower_requested = set(b.lower() for b in all_band_aliases)
                    case_insensitive_matches = lower_available.intersection(lower_requested)
                    
                    if case_insensitive_matches:
                        logger.warning(f"Found case-insensitive matches: {case_insensitive_matches}")
                    
                    logger.error("Please check your band name mappings in the catalog client.")
                    return pa.Table.from_pylist([], schema=assets.schema)
            
            # Apply limit if provided
            if limit and limit > 0 and len(assets) > limit:
                # Get the newest scenes up to the limit
                assets = assets.sort_by([("datetime", "descending")]).slice(0, limit)
            
            logger.info(f"Query returned {len(assets)} assets")
            return assets
            
        except Exception as e:
            logger.error(f"Error querying catalog: {e}")
            # Return empty table
            return pa.Table.from_pylist([])