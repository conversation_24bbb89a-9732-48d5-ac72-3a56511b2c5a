# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/actor_pool.py

"""
HTTP Actor Pool Management

Manages a pool of persistent HTTP connection actors for optimal Ray-based
satellite image processing. Provides round-robin distribution and fault
tolerance for HTTP actors with persistent aiohttp sessions.
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any

import ray

from .http_pool import HTTPConnectionPool

logger = logging.getLogger(__name__)


@ray.remote
class HTTPActorPool:
    """
    Manages a pool of HTTPConnectionPool actors for distributed HTTP operations.
    
    Provides round-robin distribution, fault tolerance, and lifecycle management
    for persistent HTTP connection actors. Enables optimal connection reuse
    across Ray tasks while maintaining actor isolation.
    """
    
    def __init__(self, pool_size: int = 4, max_connections_per_actor: int = 25):
        """
        Initialize HTTP actor pool manager.
        
        Args:
            pool_size: Number of HTTP connection pool actors to create
            max_connections_per_actor: Max connections per individual actor
        """
        self.pool_size = pool_size
        self.max_connections_per_actor = max_connections_per_actor
        self.actors: List[ray.ObjectRef] = []
        self.round_robin_index = 0
        self.actor_stats = {}
        
    async def initialize_pool(self):
        """
        Create and initialize the pool of HTTP connection actors.
        
        Creates named actors with detached lifetime for persistence across
        driver restarts. Attempts to retrieve existing actors first to
        enable seamless restarts.
        """
        try:
            self.actors = []
            
            for i in range(self.pool_size):
                actor_name = f"http_connection_pool_{i}"
                
                try:
                    # Try to retrieve existing actor first
                    actor = ray.get_actor(actor_name)
                    logger.info(f"Retrieved existing HTTP actor: {actor_name}")
                    
                    # Verify actor exists and is responsive
                    try:
                        await actor.get_stats.remote()
                        logger.info(f"Retrieved existing actor {actor_name}")
                    except Exception as e:
                        logger.warning(f"Actor {actor_name} not responsive, recreating: {e}")
                        raise ValueError("Actor not responsive")
                        
                except ValueError:
                    # Create new actor if doesn't exist or unhealthy
                    logger.info(f"Creating new HTTP actor: {actor_name}")
                    actor = HTTPConnectionPool.options(
                        name=actor_name,
                        lifetime="detached",  # Persist beyond driver restarts
                        max_restarts=3,       # Auto-restart on failures
                        max_task_retries=2    # Retry failed tasks
                    ).remote(
                        pool_size=1,  # Each actor manages 1 session pool
                        max_connections=self.max_connections_per_actor
                    )
                    
                    # Initialize the actor
                    await actor.initialize.remote()
                    logger.info(f"Initialized new HTTP actor: {actor_name}")
                
                self.actors.append(actor)
                self.actor_stats[actor_name] = {"requests": 0, "errors": 0}
            
            logger.info(f"HTTP actor pool initialized with {len(self.actors)} actors")
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTP actor pool: {e}")
            raise
    
    async def get_next_actor(self) -> ray.ObjectRef:
        """
        Get the next available HTTP actor using round-robin distribution.
        
        Returns:
            Ray object reference to an HTTP connection pool actor
        """
        if not self.actors:
            raise RuntimeError("HTTP actor pool not initialized")
        
        actor = self.actors[self.round_robin_index]
        self.round_robin_index = (self.round_robin_index + 1) % len(self.actors)
        
        return actor
    
    async def fetch_batch_distributed(self, window_specs_batch: List, 
                                    batch_size: int = 10) -> Dict[str, bytes]:
        """
        Distribute a large batch across multiple HTTP actors for parallel processing.
        
        Args:
            window_specs_batch: List of WindowSpec objects to fetch
            batch_size: Number of specs per actor batch
            
        Returns:
            Combined results from all actors
        """
        if not window_specs_batch:
            return {}
        
        try:
            # Split batch across actors
            actor_tasks = []
            
            for i in range(0, len(window_specs_batch), batch_size):
                batch = window_specs_batch[i:i + batch_size]
                actor = await self.get_next_actor()
                
                task = actor.fetch_batch.remote(batch)
                actor_tasks.append(task)
            
            # Wait for all actor tasks to complete
            results_list = await asyncio.gather(*[
                ray.get(task) for task in actor_tasks
            ], return_exceptions=True)
            
            # Combine results from all actors
            combined_results = {}
            for result in results_list:
                if isinstance(result, Exception):
                    logger.error(f"Actor batch failed: {result}")
                elif isinstance(result, dict):
                    combined_results.update(result)
            
            logger.info(f"Distributed fetch completed: {len(combined_results)} results")
            return combined_results
            
        except Exception as e:
            logger.error(f"Distributed fetch failed: {e}")
            return {}
    
    async def get_pool_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics from all HTTP actors in the pool.
        
        Returns:
            Aggregated statistics from all actors
        """
        try:
            stats_tasks = [actor.get_stats.remote() for actor in self.actors]
            actor_stats_list = await asyncio.gather(*[
                ray.get(task) for task in stats_tasks
            ], return_exceptions=True)
            
            # Aggregate statistics
            total_stats = {
                "pool_size": len(self.actors),
                "total_requests": 0,
                "total_bytes": 0,
                "total_errors": 0,
                "total_connection_reuses": 0,
                "actor_details": []
            }
            
            for i, stats in enumerate(actor_stats_list):
                if isinstance(stats, Exception):
                    logger.error(f"Failed to get stats from actor {i}: {stats}")
                    continue
                
                if isinstance(stats, dict):
                    total_stats["total_requests"] += stats.get("requests_processed", 0)
                    total_stats["total_bytes"] += stats.get("bytes_fetched", 0)
                    total_stats["total_errors"] += stats.get("errors", 0)
                    total_stats["total_connection_reuses"] += stats.get("connection_reuses", 0)
                    total_stats["actor_details"].append(stats)
            
            return total_stats
            
        except Exception as e:
            logger.error(f"Failed to get pool stats: {e}")
            return {"error": str(e)}
    

    
    async def cleanup_pool(self):
        """
        Clean shutdown of all actors in the pool.
        
        Gracefully closes all HTTP sessions and cleans up resources.
        """
        try:
            cleanup_tasks = [actor.cleanup.remote() for actor in self.actors]
            await asyncio.gather(*[
                ray.get(task) for task in cleanup_tasks
            ], return_exceptions=True)
            
            self.actors.clear()
            logger.info("HTTP actor pool cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Pool cleanup error: {e}")


# Singleton instance for global access
_http_actor_pool: Optional[HTTPActorPool] = None


async def get_http_actor_pool() -> HTTPActorPool:
    """
    Get or create the global HTTP actor pool instance.
    
    Returns:
        Singleton HTTPActorPool instance
    """
    global _http_actor_pool
    
    if _http_actor_pool is None:
        try:
            # Try to get existing pool actor
            pool_actor = ray.get_actor("http_actor_pool_manager")
            _http_actor_pool = pool_actor
            logger.info("Retrieved existing HTTP actor pool manager")
            
        except ValueError:
            # Create new pool manager
            _http_actor_pool = HTTPActorPool.options(
                name="http_actor_pool_manager",
                lifetime="detached"
            ).remote()
            
            await _http_actor_pool.initialize_pool.remote()
            logger.info("Created new HTTP actor pool manager")
    
    return _http_actor_pool


async def cleanup_global_pool():
    """Clean up the global HTTP actor pool."""
    global _http_actor_pool
    
    if _http_actor_pool:
        try:
            await _http_actor_pool.cleanup_pool.remote()
            _http_actor_pool = None
            logger.info("Global HTTP actor pool cleaned up")
        except Exception as e:
            logger.error(f"Global pool cleanup error: {e}")
