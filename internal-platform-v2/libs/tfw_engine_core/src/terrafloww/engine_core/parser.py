"""
COG header parsing utilities.
"""
import struct
import logging, sys
from typing import Dict, Optional, Any

import httpx

# Import from the local fetch module
from .fetch import _fetch_range

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Mapping from TIFF tag types to struct format characters
TIFF_TYPE_FORMAT = {
    1: "B",  # BYTE
    2: "c",  # ASCII
    3: "H",  # SHORT
    4: "L",  # LONG
    5: "LL",  # RATIONAL (2 LONGS)
    6: "b",  # SBYTE
    7: "B",  # UNDEFINED (treat as BYTE)
    8: "h",  # SSHORT
    9: "l",  # SLONG
    10: "ll",  # SRATIONAL (2 SLONGS)
    11: "f",  # FLOAT
    12: "d",  # DOUBLE
}

# Size of each TIFF type in bytes
TIFF_TYPE_SIZE = {
    1: 1,
    2: 1,
    3: 2,
    4: 4,
    5: 8,
    6: 1,
    7: 1,
    8: 2,
    9: 4,
    10: 8,
    11: 4,
    12: 8,
}

# Data type map (as used in rasteret CogMetadata)
DTYPE_MAP = {
    (1, 8): "uint8",
    (1, 16): "uint16",
    (1, 32): "uint32",
    (2, 8): "int8",
    (2, 16): "int16",
    (2, 32): "int32",
    (3, 32): "float32",
    (3, 64): "float64",
}

def _get_dtype_str(sample_format: int, bits_per_sample: int) -> str:
    """Maps TIFF SampleFormat and BitsPerSample to numpy dtype string."""
    return DTYPE_MAP.get((sample_format, bits_per_sample), f"uint{bits_per_sample}")

async def _parse_tiff_tag_value(
    client: httpx.AsyncClient,
    url: str,
    type_id: int,
    count: int,
    value_or_offset_bytes: bytes,
    endian: str,
) -> Optional[Any]:
    """Helper to parse a TIFF tag value, fetching data if needed."""
    try:
        fmt_char = TIFF_TYPE_FORMAT.get(type_id)
        size = TIFF_TYPE_SIZE.get(type_id)
        if not fmt_char or not size:
            return None
        total_size = size * count
        value_bytes = value_or_offset_bytes

        if total_size > len(value_or_offset_bytes):
            offset_fmt = f"{endian}L"
            if len(value_or_offset_bytes) < 4:
                return None
            offset = struct.unpack(offset_fmt, value_or_offset_bytes[:4])[0]
            value_bytes = await _fetch_range(client, url, offset, total_size)
            if len(value_bytes) != total_size:
                return None
        else:
            value_bytes = value_bytes[:total_size]

        full_fmt = f"{endian}{count}{fmt_char}"
        if type_id == 2:
            return value_bytes.partition(b"\0")[0].decode("ascii", errors="ignore")
        elif type_id == 5 or type_id == 10:
            num_items = count * 2
            full_fmt = f"{endian}{num_items}{fmt_char[0]}"
            vals = struct.unpack(full_fmt, value_bytes)
            return [
                float(vals[i]) / vals[i + 1] if vals[i + 1] != 0 else float("inf")
                for i in range(0, num_items, 2)
            ]
        else:
            if len(value_bytes) != struct.calcsize(full_fmt):
                return None
            return struct.unpack(full_fmt, value_bytes)

    except Exception as e:
        logger.debug(f"Error parsing tag value (type={type_id}, count={count}): {e}")
        return None

async def parse_cog_header_info(client: httpx.AsyncClient, url: str) -> Optional[Dict[str, Any]]:
    """
    Parses essential COG header information using the provided httpx client.
    """
    try:
        header_bytes = await _fetch_range(client, url, 0, 24)
        if not header_bytes or len(header_bytes) < 8:
            logger.error(f"Failed to fetch TIFF header from {url}")
            return None

        if header_bytes[0:2] == b"II":
            endian = "<"
        elif header_bytes[0:2] == b"MM":
            endian = ">"
        else:
            logger.error(f"Invalid TIFF ID for {url}")
            return None

        version = struct.unpack(f"{endian}H", header_bytes[2:4])[0]
        offset_size = 0
        ifd_offset = 0
        entry_size = 0
        ifd_count_size = 0

        if version == 42:
            offset_size = 4
            ifd_offset = struct.unpack(f"{endian}L", header_bytes[4:8])[0]
            entry_size = 12
            ifd_count_size = 2
        elif version == 43:
            if struct.unpack(f"{endian}H", header_bytes[4:6])[0] != 8:
                return None
            offset_size = 8
            ifd_offset = struct.unpack(f"{endian}Q", header_bytes[8:16])[0]
            entry_size = 20
            ifd_count_size = 8
        else:
            logger.error(f"Unsupported TIFF version {version} for {url}")
            return None

        if ifd_offset == 0:
            logger.error(f"IFD offset is zero for {url}")
            return None

        ifd_count_bytes = await _fetch_range(client, url, ifd_offset, ifd_count_size)
        if not ifd_count_bytes or len(ifd_count_bytes) < ifd_count_size:
            logger.error(f"Failed to fetch IFD count from {url}")
            return None

        if ifd_count_size == 2:
            entry_count = struct.unpack(f"{endian}H", ifd_count_bytes)[0]
        else:
            entry_count = struct.unpack(f"{endian}Q", ifd_count_bytes)[0]

        ifd_bytes = await _fetch_range(
            client, url, ifd_offset + ifd_count_size, entry_count * entry_size
        )
        if not ifd_bytes or len(ifd_bytes) < entry_count * entry_size:
            logger.error(f"Failed to fetch IFD entries from {url}")
            return None

        tags = {}
        for i in range(entry_count):
            entry = ifd_bytes[i * entry_size : (i + 1) * entry_size]
            tag_id = struct.unpack(f"{endian}H", entry[0:2])[0]
            type_id = struct.unpack(f"{endian}H", entry[2:4])[0]
            if version == 42:
                count = struct.unpack(f"{endian}L", entry[4:8])[0]
                value_or_offset_bytes = entry[8:12]
            else:
                count = struct.unpack(f"{endian}Q", entry[4:12])[0]
                value_or_offset_bytes = entry[12:20]

            parsed_value = await _parse_tiff_tag_value(
                client, url, type_id, count, value_or_offset_bytes, endian
            )
            if parsed_value is not None:
                tags[tag_id] = parsed_value

        image_width = tags.get(256, [None])[0]
        image_height = tags.get(257, [None])[0]
        if image_width is None or image_height is None:
            return None

        tile_width = tags.get(322, [image_width])[0]
        tile_height = tags.get(323, tags.get(278, [image_height]))[0]
        tile_offsets = list(tags.get(324, tags.get(273, [])))
        tile_byte_counts = list(tags.get(325, tags.get(279, [])))
        if not tile_offsets or not tile_byte_counts:
            return None

        bits_per_sample = tags.get(258, [8])[0]
        sample_format = tags.get(339, [1])[0]
        dtype_str = _get_dtype_str(sample_format, bits_per_sample)
        compression = tags.get(259, [1])[0]
        predictor = tags.get(317, [1])[0]

        transform = None
        if 34264 in tags:
            matrix = tags[34264]
            transform = (
                (matrix[0], matrix[1], matrix[3], matrix[4], matrix[5], matrix[7])
                if len(matrix) == 16
                else None
            )
        elif 33922 in tags and 33550 in tags:
            tiepoints = tags[33922]
            scales = tags[33550]
            transform = (
                (scales[0], 0.0, tiepoints[3], 0.0, -scales[1], tiepoints[4])
                if len(tiepoints) >= 6 and len(scales) >= 2
                else None
            )
        if transform is None:
            return None

        crs_string = None
        if 34737 in tags and isinstance(tags[34737], str):
            import re
            match = re.search(r'AUTHORITY\["EPSG",(\d+)\]', tags[34737].upper())
            crs_string = f"EPSG:{match.group(1)}" if match else None
        if not crs_string and 34735 in tags:
            try:
                geokeys = tags[34735]
                num_keys = geokeys[3]
                for i in range(4, 4 + (4 * num_keys), 4):
                    key_id = geokeys[i]
                    tag_loc = geokeys[i + 1]
                    count = geokeys[i + 2]
                    val_off = geokeys[i + 3]
                    if key_id in (3072, 2048) and tag_loc == 0:
                        crs_string = f"EPSG:{val_off}"
                        break
            except Exception:
                pass
        if not crs_string:
            return None

        return {
            "width": image_width,
            "height": image_height,
            "tile_width": tile_width,
            "tile_height": tile_height,
            "transform": transform,
            "crs_string": crs_string,
            "dtype_str": dtype_str,
            "predictor": predictor,
            "tile_offsets": tile_offsets,
            "tile_byte_counts": tile_byte_counts,
            "compression": compression,
        }

    except Exception as e:
        logger.error(f"Failed to parse COG header for {url}: {e}", exc_info=True)
        return None
