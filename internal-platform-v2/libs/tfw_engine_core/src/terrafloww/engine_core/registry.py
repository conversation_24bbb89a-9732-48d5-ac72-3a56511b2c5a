"""Registry for server-side operation functions.

This module provides a registry mechanism for registering functions that can be executed on
the server side. Functions are registered with a name and can be retrieved by that name or by
a fully qualified name with a namespace (e.g., "terrafloww.spectral.ndvi").
"""

import logging, sys

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

FUNCTIONS: dict[str, callable] = {}

# Map of namespace names to core names
def _get_core_name(func_id: str) -> str:
    """Get the core name from a namespaced function ID."""
    if "." in func_id:
        # Extract the last part after the last dot
        return func_id.split(".")[-1]
    return func_id

def register(name: str):
    """Register a function with the given name.
    
    Args:
        name: The name to register the function under.
        
    Returns:
        A decorator function that registers the decorated function.
    """
    def _decor(fn: callable):
        FUNCTIONS[name] = fn
        logger.info(f"Registered function '{name}' with signature: {fn.__name__}{fn.__annotations__}")
        return fn
    return _decor

def get(name: str) -> callable:
    """Get a registered function by name.
    
    Args:
        name: The name of the function to retrieve. Must match exactly.
        
    Returns:
        The registered function.
        
    Raises:
        KeyError: If the function is not registered.
    """
    if name not in FUNCTIONS:
        logger.error(f"Function '{name}' not found in registry. Available functions: {list(FUNCTIONS.keys())}")
        raise KeyError(f"Function '{name}' not found in registry")
    
    fn = FUNCTIONS[name]
    logger.info(f"Retrieved function '{name}' with signature: {fn.__name__}{fn.__annotations__}")
    return fn

def list_registered_functions() -> list[str]:
    """List all registered function names.
    
    Returns:
        A list of registered function names.
    """
    return list(FUNCTIONS.keys())
