[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tfw-engine-core"
version = "0.1.0"
description = "Core COG reading and processing utilities for Terrafloww."
readme = "README.md"
requires-python = ">=3.10"
license = { text = "Proprietary" }
authors = [
  { name = "Terrafloww Team" },
]
dependencies = [
  "httpx[http2]>=0.25",
  "aiohttp>=3.9.0",
  "pyarrow>=15.0",
  "numpy>=1.20",
  "shapely>=2.0",
  "rasterio>=1.3",
  "affine>=2.3",
  "pyproj>=3.0",
  "imagecodecs>=2023.0.0",
  "cachetools>=5.0",
  "pytz",
  # S3/Cloud storage dependencies
  "boto3>=1.29.0",
  "s3fs>=2024.2.0",
  "deltalake==0.25.5"
]

[project.urls]
Homepage    = "https://github.com/terrafloww/platform"

[tool.setuptools]
package-dir = {"" = "src"}          # tells setuptools to look under src/

[tool.setuptools.packages.find]
where  = ["src"]
include = ["terrafloww.*"]

