from google.protobuf import struct_pb2 as _struct_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class LoadParameters(_message.Message):
    __slots__ = ("collection", "bands", "aoi_wkt", "aoi_crs", "datetime_filter", "property_filters", "scene_limit", "catalog_identifier")
    COLLECTION_FIELD_NUMBER: _ClassVar[int]
    BANDS_FIELD_NUMBER: _ClassVar[int]
    AOI_WKT_FIELD_NUMBER: _ClassVar[int]
    AOI_CRS_FIELD_NUMBER: _ClassVar[int]
    DATETIME_FILTER_FIELD_NUMBER: _ClassVar[int]
    PROPERTY_FILTERS_FIELD_NUMBER: _ClassVar[int]
    SCENE_LIMIT_FIELD_NUMBER: _ClassVar[int]
    CATALOG_IDENTIFIER_FIELD_NUMBER: _ClassVar[int]
    collection: str
    bands: _containers.RepeatedScalarFieldContainer[str]
    aoi_wkt: str
    aoi_crs: str
    datetime_filter: str
    property_filters: _struct_pb2.Struct
    scene_limit: int
    catalog_identifier: str
    def __init__(self, collection: _Optional[str] = ..., bands: _Optional[_Iterable[str]] = ..., aoi_wkt: _Optional[str] = ..., aoi_crs: _Optional[str] = ..., datetime_filter: _Optional[str] = ..., property_filters: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ..., scene_limit: _Optional[int] = ..., catalog_identifier: _Optional[str] = ...) -> None: ...

class ApplyFunction(_message.Message):
    __slots__ = ("function_id", "parameters")
    FUNCTION_ID_FIELD_NUMBER: _ClassVar[int]
    PARAMETERS_FIELD_NUMBER: _ClassVar[int]
    function_id: str
    parameters: _struct_pb2.Struct
    def __init__(self, function_id: _Optional[str] = ..., parameters: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ...) -> None: ...

class WorkflowPlan(_message.Message):
    __slots__ = ("load_step", "apply_steps", "head_limit")
    LOAD_STEP_FIELD_NUMBER: _ClassVar[int]
    APPLY_STEPS_FIELD_NUMBER: _ClassVar[int]
    HEAD_LIMIT_FIELD_NUMBER: _ClassVar[int]
    load_step: LoadParameters
    apply_steps: _containers.RepeatedCompositeFieldContainer[ApplyFunction]
    head_limit: int
    def __init__(self, load_step: _Optional[_Union[LoadParameters, _Mapping]] = ..., apply_steps: _Optional[_Iterable[_Union[ApplyFunction, _Mapping]]] = ..., head_limit: _Optional[int] = ...) -> None: ...

class ExecuteWorkflowRequest(_message.Message):
    __slots__ = ("plan", "execution_mode", "job_id")
    class ExecutionMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        EXECUTION_MODE_UNSPECIFIED: _ClassVar[ExecuteWorkflowRequest.ExecutionMode]
        SEQUENTIAL: _ClassVar[ExecuteWorkflowRequest.ExecutionMode]
        RAY: _ClassVar[ExecuteWorkflowRequest.ExecutionMode]
    EXECUTION_MODE_UNSPECIFIED: ExecuteWorkflowRequest.ExecutionMode
    SEQUENTIAL: ExecuteWorkflowRequest.ExecutionMode
    RAY: ExecuteWorkflowRequest.ExecutionMode
    PLAN_FIELD_NUMBER: _ClassVar[int]
    EXECUTION_MODE_FIELD_NUMBER: _ClassVar[int]
    JOB_ID_FIELD_NUMBER: _ClassVar[int]
    plan: WorkflowPlan
    execution_mode: ExecuteWorkflowRequest.ExecutionMode
    job_id: str
    def __init__(self, plan: _Optional[_Union[WorkflowPlan, _Mapping]] = ..., execution_mode: _Optional[_Union[ExecuteWorkflowRequest.ExecutionMode, str]] = ..., job_id: _Optional[str] = ...) -> None: ...

class ExecuteWorkflowResponse(_message.Message):
    __slots__ = ("execution_id", "flight_ticket", "status_message")
    EXECUTION_ID_FIELD_NUMBER: _ClassVar[int]
    FLIGHT_TICKET_FIELD_NUMBER: _ClassVar[int]
    STATUS_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    execution_id: str
    flight_ticket: str
    status_message: str
    def __init__(self, execution_id: _Optional[str] = ..., flight_ticket: _Optional[str] = ..., status_message: _Optional[str] = ...) -> None: ...
