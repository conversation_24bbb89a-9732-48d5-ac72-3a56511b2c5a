# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: terrafloww/processing_engine/v1/processing_engine.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'terrafloww/processing_engine/v1/processing_engine.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7terrafloww/processing_engine/v1/processing_engine.proto\x12\x1fterrafloww.processing_engine.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd2\x01\n\x0eLoadParameters\x12\x12\n\ncollection\x18\x01 \x01(\t\x12\r\n\x05\x62\x61nds\x18\x02 \x03(\t\x12\x0f\n\x07\x61oi_wkt\x18\x03 \x01(\t\x12\x0f\n\x07\x61oi_crs\x18\x04 \x01(\t\x12\x17\n\x0f\x64\x61tetime_filter\x18\x05 \x01(\t\x12\x31\n\x10property_filters\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x13\n\x0bscene_limit\x18\x07 \x01(\x05\x12\x1a\n\x12\x63\x61talog_identifier\x18\x08 \x01(\t\"Q\n\rApplyFunction\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12+\n\nparameters\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xab\x01\n\x0cWorkflowPlan\x12\x42\n\tload_step\x18\x01 \x01(\x0b\x32/.terrafloww.processing_engine.v1.LoadParameters\x12\x43\n\x0b\x61pply_steps\x18\x02 \x03(\x0b\x32..terrafloww.processing_engine.v1.ApplyFunction\x12\x12\n\nhead_limit\x18\x03 \x01(\x05\"\x8e\x02\n\x16\x45xecuteWorkflowRequest\x12;\n\x04plan\x18\x01 \x01(\x0b\x32-.terrafloww.processing_engine.v1.WorkflowPlan\x12]\n\x0e\x65xecution_mode\x18\x02 \x01(\x0e\x32\x45.terrafloww.processing_engine.v1.ExecuteWorkflowRequest.ExecutionMode\x12\x0e\n\x06job_id\x18\x03 \x01(\t\"H\n\rExecutionMode\x12\x1e\n\x1a\x45XECUTION_MODE_UNSPECIFIED\x10\x00\x12\x0e\n\nSEQUENTIAL\x10\x01\x12\x07\n\x03RAY\x10\x02\"^\n\x17\x45xecuteWorkflowResponse\x12\x14\n\x0c\x65xecution_id\x18\x01 \x01(\t\x12\x15\n\rflight_ticket\x18\x02 \x01(\t\x12\x16\n\x0estatus_message\x18\x03 \x01(\t2\xa0\x01\n\x17ProcessingEngineService\x12\x84\x01\n\x0f\x45xecuteWorkflow\x12\x37.terrafloww.processing_engine.v1.ExecuteWorkflowRequest\x1a\x38.terrafloww.processing_engine.v1.ExecuteWorkflowResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'terrafloww.processing_engine.v1.processing_engine_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_LOADPARAMETERS']._serialized_start=156
  _globals['_LOADPARAMETERS']._serialized_end=366
  _globals['_APPLYFUNCTION']._serialized_start=368
  _globals['_APPLYFUNCTION']._serialized_end=449
  _globals['_WORKFLOWPLAN']._serialized_start=452
  _globals['_WORKFLOWPLAN']._serialized_end=623
  _globals['_EXECUTEWORKFLOWREQUEST']._serialized_start=626
  _globals['_EXECUTEWORKFLOWREQUEST']._serialized_end=896
  _globals['_EXECUTEWORKFLOWREQUEST_EXECUTIONMODE']._serialized_start=824
  _globals['_EXECUTEWORKFLOWREQUEST_EXECUTIONMODE']._serialized_end=896
  _globals['_EXECUTEWORKFLOWRESPONSE']._serialized_start=898
  _globals['_EXECUTEWORKFLOWRESPONSE']._serialized_end=992
  _globals['_PROCESSINGENGINESERVICE']._serialized_start=995
  _globals['_PROCESSINGENGINESERVICE']._serialized_end=1155
# @@protoc_insertion_point(module_scope)
