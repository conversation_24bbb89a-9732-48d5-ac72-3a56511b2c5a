"""Terrafloww Raster Schemas Package."""

from .raster import RASTER_CHUNK_SCHEMA
from .stac import EXT_STAC_IMG_DATASETS_SCHEMA, WINDOW_DEFINITION_SCHEMA, PROCESSING_WINDOW_SCHEMA
from .datasets import IMG_DATASETS_SCHEMA
from .grid import GRID_TEMPLATES_SCHEMA
from .operations import (
    OPERATIONS_SCHEMA,
    get_operation_schema,
    list_available_operations,
    get_operations_by_category,
    get_required_bands_for_operation,
)

__all__ = [
    # Raster and STAC schemas
    "RASTER_CHUNK_SCHEMA",
    "EXT_STAC_IMG_DATASETS_SCHEMA",
    "WINDOW_DEFINITION_SCHEMA",
    "PROCESSING_WINDOW_SCHEMA",
    
    # Dataset and grid schemas
    "IMG_DATASETS_SCHEMA",
    "GRID_TEMPLATES_SCHEMA",
    
    # Operations schema and utilities
    "OPERATIONS_SCHEMA",
    "get_operation_schema",
    "list_available_operations",
    "get_operations_by_category",
    "get_required_bands_for_operation",
]
