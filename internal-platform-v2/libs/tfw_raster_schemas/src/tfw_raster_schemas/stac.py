"""STAC schema definitions for Terrafloww Platform."""

import pyarrow as pa

# Define the schema for the external STAC datasets catalog table
# One row per relevant COG Asset, repeating scene-level info.
EXT_STAC_IMG_DATASETS_SCHEMA = pa.schema(
    [
        # --- Scene Identification & Core STAC Info (Repeated per Asset) ---
        pa.field(
            "scene_id",
            pa.string(),
            nullable=False,
            metadata={"description": "Original unique Scene ID from STAC Item"},
        ),
        pa.field(
            "collection",
            pa.string(),
            nullable=False,
            metadata={"description": "STAC Collection ID (e.g., 'sentinel-2-l2a')"},
        ),
        pa.field(
            "datetime",
            pa.timestamp("us", tz="UTC"),
            nullable=False,
            metadata={"description": "Scene acquisition timestamp"},
        ),
        # --- Partitioning Columns ---
        pa.field(
            "year",
            pa.int32(),
            nullable=False,
            metadata={"description": "Year derived from datetime"},
        ),
        pa.field(
            "month",
            pa.int32(),
            nullable=False,
            metadata={"description": "Month derived from datetime"},
        ),
        # --- Spatial Info (Repeated per Asset) ---
        pa.field(
            "geometry_wkt",
            pa.string(),
            nullable=True,  # Store as WKT string
            metadata={"description": "Scene footprint geometry as WKT string"},
        ),
        pa.field(
            "bbox_minx",
            pa.float64(),
            nullable=True,
            metadata={"description": "Bounding box minimum longitude/X"},
        ),
        pa.field(
            "bbox_miny",
            pa.float64(),
            nullable=True,
            metadata={"description": "Bounding box minimum latitude/Y"},
        ),
        pa.field(
            "bbox_maxx",
            pa.float64(),
            nullable=True,
            metadata={"description": "Bounding box maximum longitude/X"},
        ),
        pa.field(
            "bbox_maxy",
            pa.float64(),
            nullable=True,
            metadata={"description": "Bounding box maximum latitude/Y"},
        ),
        # --- Common Properties (Extracted & Repeated per Asset) ---
        pa.field(
            "cloud_cover",
            pa.float64(),
            nullable=True,
            metadata={
                "description": "Cloud cover percentage (e.g., extracted from 'eo:cloud_cover')"
            },
        ),
        # --- Full Original Metadata (Repeated per Asset, for completeness) ---
        pa.field(
            "stac_properties_json",
            pa.string(),
            nullable=True,
            metadata={
                "description": "Full original STAC Item properties as JSON string"
            },
        ),
        pa.field(
            "stac_assets_json",
            pa.string(),
            nullable=True,
            metadata={
                "description": "Full original STAC Item assets dictionary as JSON string"
            },
        ),
        # --- Asset-Specific Information (The Key Part of Flattening) ---
        pa.field(
            "cog_key",
            pa.string(),
            nullable=False,
            metadata={
                "description": "The key/name of this specific asset (e.g., 'B04', 'SR_B5')"
            },
        ),
        pa.field(
            "cog_href",
            pa.string(),
            nullable=False,
            metadata={"description": "Absolute HREF/URL for this COG asset"},
        ),
        pa.field(
            "cog_title",
            pa.string(),
            nullable=True,
            metadata={"description": "Title of the asset from STAC"},
        ),
        pa.field(
            "cog_roles",
            pa.list_(pa.string()),
            nullable=True,
            metadata={"description": "Roles of the asset from STAC (e.g., ['data'])"},
        ),
        # --- Parsed COG Metadata (Top-Level Columns for Efficiency) ---
        pa.field(
            "cog_width",
            pa.int64(),
            nullable=False,
            metadata={"description": "Image width in pixels for this asset's COG"},
        ),
        pa.field(
            "cog_height",
            pa.int64(),
            nullable=False,
            metadata={"description": "Image height in pixels for this asset's COG"},
        ),
        pa.field(
            "cog_tile_width",
            pa.int64(),
            nullable=False,
            metadata={
                "description": "Internal tile width in pixels for this asset's COG"
            },
        ),
        pa.field(
            "cog_tile_height",
            pa.int64(),
            nullable=False,
            metadata={
                "description": "Internal tile height in pixels for this asset's COG"
            },
        ),
        pa.field(
            "cog_transform",
            pa.list_(pa.float64(), 6),
            nullable=False,
            metadata={"description": "Affine transform (GDAL order: c, a, b, f, d, e)"},
        ),
        pa.field(
            "cog_crs",
            pa.string(),
            nullable=False,
            metadata={
                "description": "CRS string from COG header (e.g., 'EPSG:4326')"
            },
        ),
        pa.field(
            "cog_scale",
            pa.float64(),
            nullable=True,
            metadata={
                "description": "Scale factor to be applied to the raw pixel values"
            },
        ),
        pa.field(
            "cog_offset",
            pa.float64(),
            nullable=True,
            metadata={
                "description": "Offset to be added to the scaled pixel values"
            },
        ),
        pa.field(
            "cog_dtype",
            pa.string(),
            nullable=False,
            metadata={"description": "NumPy dtype string (e.g., 'uint16')"},
        ),
        pa.field(
            "cog_predictor",
            pa.int32(),
            nullable=True,  # Predictor might not always be present or relevant
            metadata={
                "description": "TIFF Predictor value (e.g., 1=None, 2=Horizontal)"
            },
        ),
        pa.field(
            "cog_compression",
            pa.string(),
            nullable=True,  # Store compression type if available
            metadata={"description": "Compression method (e.g., 'deflate', 'lzw')"},
        ),
        pa.field(
            "cog_tile_offsets",
            pa.list_(pa.int64()),
            nullable=False,
            metadata={"description": "List of byte offsets for COG tiles"},
        ),
        pa.field(
            "cog_tile_byte_counts",
            pa.list_(pa.int64()),
            nullable=False,
            metadata={"description": "List of byte counts for COG tiles"},
        ),
    ],
    metadata={
        b"description": b"Catalog of externally hosted STAC COG assets with pre-parsed metadata.",
        b"partitioning_strategy": b"collection/year/month",
    },
)

# Schema for window definitions used in processing
WINDOW_DEFINITION_SCHEMA = pa.schema(
    [
        pa.field("cog_href", pa.string(), nullable=False, metadata={"description": "URL of the COG asset"}),
        pa.field("offset", pa.list_(pa.int64(), 2), nullable=False, metadata={"description": "Window offset [x, y]"}),
        pa.field("size", pa.list_(pa.int64(), 2), nullable=False, metadata={"description": "Window size [width, height]"}),
        pa.field("transform", pa.list_(pa.float64(), 6), nullable=False, metadata={"description": "Affine transform for the window"}),
        pa.field("crs", pa.string(), nullable=False, metadata={"description": "Coordinate Reference System"}),
        pa.field("band", pa.string(), nullable=False, metadata={"description": "Band name"}),
        pa.field("scene_id", pa.string(), nullable=False, metadata={"description": "Scene ID"}),
        pa.field("collection", pa.string(), nullable=False, metadata={"description": "Collection name"}),
        pa.field("datetime", pa.timestamp("us", tz="UTC"), nullable=False, metadata={"description": "Scene acquisition time"}),
    ],
    metadata={b"description": b"Schema for window definitions used in processing."}
)

# Schema for internal processing windows, extends WINDOW_DEFINITION_SCHEMA with processing fields
PROCESSING_WINDOW_SCHEMA = pa.schema(
    list(WINDOW_DEFINITION_SCHEMA) + [
        pa.field("tile_r", pa.int64(), nullable=False, metadata={"description": "Tile row index"}),
        pa.field("tile_c", pa.int64(), nullable=False, metadata={"description": "Tile column index"}),
        pa.field("byte_offset", pa.int64(), nullable=False, metadata={"description": "Byte offset in COG file"}),
        pa.field("byte_size", pa.int64(), nullable=False, metadata={"description": "Byte size in COG file"}),
        pa.field("tile_shape_decode_h", pa.int64(), nullable=False, metadata={"description": "Internal tile height for decoding"}),
        pa.field("tile_shape_decode_w", pa.int64(), nullable=False, metadata={"description": "Internal tile width for decoding"}),
        pa.field("expected_shape_h", pa.int64(), nullable=False, metadata={"description": "Expected output height after decoding"}),
        pa.field("expected_shape_w", pa.int64(), nullable=False, metadata={"description": "Expected output width after decoding"}),
        pa.field("ref_transform_0", pa.float64(), nullable=False, metadata={"description": "Reference transform component 0"}),
        pa.field("ref_transform_1", pa.float64(), nullable=False, metadata={"description": "Reference transform component 1"}),
        pa.field("ref_transform_2", pa.float64(), nullable=False, metadata={"description": "Reference transform component 2"}),
        pa.field("ref_transform_3", pa.float64(), nullable=False, metadata={"description": "Reference transform component 3"}),
        pa.field("ref_transform_4", pa.float64(), nullable=False, metadata={"description": "Reference transform component 4"}),
        pa.field("ref_transform_5", pa.float64(), nullable=False, metadata={"description": "Reference transform component 5"}),
        pa.field("window_col_off", pa.int64(), nullable=False, metadata={"description": "Window column offset"}),
        pa.field("window_row_off", pa.int64(), nullable=False, metadata={"description": "Window row offset"}),
        pa.field("window_width", pa.int64(), nullable=False, metadata={"description": "Window width"}),
        pa.field("window_height", pa.int64(), nullable=False, metadata={"description": "Window height"}),
        pa.field("cog_key", pa.string(), nullable=False, metadata={"description": "Source asset/band key"}),
        pa.field("dtype_str", pa.string(), nullable=False, metadata={"description": "NumPy dtype string"}),
        pa.field("predictor", pa.int32(), nullable=True, metadata={"description": "TIFF predictor value"}),
        pa.field("scale", pa.float64(), nullable=True, metadata={"description": "Scale factor for pixel values"}),
        pa.field("offset_proc", pa.float64(), nullable=True, metadata={"description": "Offset for pixel values"}),
        pa.field("cog_compression", pa.int32(), nullable=True) # <-- ADDED (use int32 or string based on parser)

    ],
    metadata={b"description": b"Schema for internal processing windows, including byte-level fields."}
)

# --- Partitioning Strategy ---
PARTITION_COLS = ["collection", "year", "month"]

# Asset keys relevant for different collections (can be externalized)
ASSETS_TO_PROCESS = {
    "sentinel-2-l2a": [
        "coastal",
        "blue",
        "green",
        "red",
        "rededge1",
        "rededge2",
        "rededge3",
        "nir",
        "nir08",
        "nir09",
        "swir16",
        "swir22",  # Core data bands
        # "scl", "aot", "wvp" # Optional QA/Aux bands
    ],
    "landsat-c2l2-sr": [
        "SR_B1",
        "SR_B2",
        "SR_B3",
        "SR_B4",
        "SR_B5",
        "SR_B6",
        "SR_B7",  # Core data bands
        # "QA_PIXEL" # Optional QA band
    ],
    # Add other collections as needed
}
