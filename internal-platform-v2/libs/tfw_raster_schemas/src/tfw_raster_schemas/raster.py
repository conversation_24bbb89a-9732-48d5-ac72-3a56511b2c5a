"""Raster schema definitions for Terrafloww Platform."""

import pyarrow as pa

# Schema for final processed raster chunks (Multi-band)
RASTER_CHUNK_SCHEMA = pa.schema(
    [
        pa.field("chunk_id", pa.string(), nullable=False, metadata={"description": "Unique ID for this processed multi-band chunk"}),
        pa.field("raster_data", pa.list_(pa.float32()), nullable=False, metadata={"description": "Flattened raster data (C*H*W) as float32"}),
        pa.field("shape", pa.list_(pa.int32(), 3), nullable=False, metadata={"description": "Shape [Channels, Height, Width]"}), # C > 1 possible
        pa.field("bounds", pa.list_(pa.float64(), 4), nullable=False, metadata={"description": "Chunk bounds [minx, miny, maxx, maxy]"}),
        pa.field("crs", pa.string(), nullable=False, metadata={"description": "Coordinate Reference System"}),
        pa.field("datetime", pa.timestamp("us", tz="UTC"), nullable=False, metadata={"description": "Observation timestamp"}),
        pa.field("bands", pa.list_(pa.string()), nullable=False, metadata={"description": "List of band names in channel order"}), # Multiple bands
        pa.field("label", pa.string(), nullable=True, metadata={"description": "Optional label"}),
        pa.field("quality", pa.map_(pa.string(), pa.float64()), nullable=True, metadata={"description": "Quality/provenance metrics"})
    ],
    metadata={ b"description": b"Schema for processed multi-band raster data chunks." }
)
