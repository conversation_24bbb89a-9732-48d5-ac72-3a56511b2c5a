"""Dataset schema definitions for Terrafloww Platform."""

import pyarrow as pa

# Schema for the 'datasets' Delta table
# Partitioning strategy: Often by 'name' or ('name', 'dataset_type')
IMG_DATASETS_SCHEMA = pa.schema([
    pa.field("dataset_id", pa.string(), nullable=False,
             metadata={"description": "Unique Platform ID for the dataset instance/version"}),
    pa.field("name", pa.string(), nullable=False,
             metadata={"description": "User-defined or generated name (e.g., s2_chips_masked_256)"}),
    pa.field("version", pa.int32(), nullable=False,
             metadata={"description": "Version number of this dataset"}),
    pa.field("dataset_type", pa.string(), nullable=False,
             metadata={"description": "Type: raw_cog, derived_chips, features, prediction_mask, etc."}),
    pa.field("commit_time", pa.timestamp("us", tz="UTC"), nullable=False,
             metadata={"description": "Timestamp of this version commit"}),
    pa.field("status", pa.string(), nullable=False,
             metadata={"description": "e.g., available, deprecated, processing"}),
    # Source tracking
    pa.field("source_job_id", pa.string(), nullable=True,
             metadata={"description": "ID of the job that generated this dataset version"}),
    pa.field("source_hash", pa.string(), nullable=True,
             metadata={"description": "Hash of source configuration"}),
    pa.field("source_artifact_versions", pa.string(), nullable=True,
             metadata={"description": "JSON string of source artifact versions"}),
    # Derived fields
    pa.field("storage_paths", pa.string(), nullable=True,
             metadata={"description": "JSON string of URIs to derived data files"}),
    pa.field("arrow_schema", pa.binary(), nullable=True,
             metadata={"description": "Serialized Arrow schema for derived datasets"}),
    # Raw fields (MS+GT)
    pa.field("cog_tile_offsets", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to list of COG tile offsets"}),
    pa.field("cog_tile_byte_counts", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to list of COG tile byte counts"}),
    pa.field("cog_grid_ids", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to its corresponding grid_definition_id"}),
    pa.field("cog_dtype", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to numpy dtype string (e.g., 'uint16')"}),
    pa.field("cog_predictor", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to TIFF predictor value (e.g., 1, 2)"}),
    pa.field("cog_scale", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to scale factor"}),
    pa.field("cog_offset", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to offset value"}),
    pa.field("raw_cog_urls", pa.string(), nullable=True,
             metadata={"description": "JSON string mapping asset key to its primary access URL"}),
    # Common fields
    pa.field("parameters", pa.string(), nullable=True,
             metadata={"description": "Parameters used by the source job (stored as JSON string)"}),
    pa.field("parameters_hash", pa.string(), nullable=True,
             metadata={"description": "Hash of parameters for efficient lookups"}),
    pa.field("quality_metrics", pa.string(), nullable=True,
             metadata={"description": "Simple data quality metrics generated (JSON string)"}),
    pa.field("raw_properties", pa.string(), nullable=True,
             metadata={"description": "Original STAC properties or other raw metadata (JSON string)"}),
    pa.field("raw_geometry", pa.string(), nullable=True,
             metadata={"description": "GeoJSON geometry string for scene footprint"}),
], metadata={b"description": b"Schema for dataset entries in the metadata service."})
