# Terrafloww Platform (V2 Architecture)

**Status**: Production Ready ✅ | **Last Updated**: June 24, 2025

## Overview

This repository contains the source code for the **Terrafloww Platform V2**, a cloud-native framework designed to streamline the development, deployment, and management of AI/ML models on satellite and aerial imagery.

This version represents a significant architectural evolution towards an **API-first design**, separating the user-facing SDK from a high-performance backend processing engine. The goal is to provide a cohesive, end-to-end solution addressing common friction points like complex data preparation, inefficient large-scale data access, lack of experiment reproducibility, and infrastructure management overhead, while ensuring high performance and scalability.

## 🚀 Recent Major Improvements (June 2025)

### ✅ Flight Server Refactor - Data Integrity Fix
- **Problem Solved**: Race conditions causing 50% data loss in Ray worker results
- **Solution**: Stateful Flight Server with direct worker uploads and completion signaling
- **Result**: 100% data integrity - `test_ndvi.py` now returns exactly 2 rows instead of 1

### ✅ Ray Worker Networking Fix - Connection Reliability
- **Problem Solved**: Connection drops due to external LoadBalancer routing ("hairpinning")
- **Solution**: Internal Kubernetes service DNS for direct pod-to-pod communication
- **Result**: Reliable connections, eliminated timeouts, improved performance

### ✅ Production-Ready Deployment
- **Immutable Image Tags**: Git SHA + timestamp for reliable deployments
- **Unified Deployment Scripts**: Single command deployment with rollback capability
- **Comprehensive Testing**: End-to-end validation with `test_ndvi.py`

## Core Concepts & Philosophy (API-First)

- **Backend Core Engine:** Encapsulates optimized, high-performance geospatial data fetching, processing, and assembly logic within a dedicated backend service.
- **API-Driven Communication:** Uses efficient, modern protocols (gRPC, Apache Arrow Flight) for communication between the SDK client and the backend engine.
- **Thin SDK Client:** Provides a user-friendly, synchronous Python interface (`load`, `apply`, `compute`, etc.) that acts primarily as a client to the backend service, defining workflows and retrieving results.
- **Unified Experience:** The same SDK code runs seamlessly whether initiated from a user's laptop, a managed notebook, or a production job, by consistently interacting with the backend processing engine.
- **Transparent Scalability:** The backend engine supports sequential and Ray-based execution paths, selectable via API request, allowing users to scale without changing SDK code.
- **Arrow-Native Communication:** Leverages Arrow Flight/IPC for efficient data streaming between the backend and the SDK client. Minimize internal data format conversions where feasible.


## High-Level Architecture (API-First)

```mermaid
graph TD
    subgraph User Environments
        Laptop["User Laptop / Local Dev"] -- SDK Client --> API_GW
        ManagedNotebook["Managed Notebook (Marimo/Jupyter)"] -- SDK Client --> API_GW
        WorkflowScript["Workflow Script (via Job Runner)"] -- SDK Client --> API_GW
    end

    subgraph "Platform Control Plane (K8s/Cloud)"
        API_GW["API Gateway (FastAPI/Envoy?) - Auth, Routing"]
        UI_Backend["UI Backend / Platform Svc - UI, Notebook Mgmt, Jobs"]
        MetadataService["Metadata Service - Artifacts, Lineage, Status (Delta)"]
        MarketplaceService["Marketplace Service - Data Discovery, Access"]
    end

    subgraph "Platform Execution Plane (K8s/Cloud)"
        ProcessingEngine["Processing Engine Svc (Python/gRPC/Flight) - Planner, Fetch/Process/Assemble, Seq/Ray Paths"]
        RayCluster["Ray Cluster (Optional) - Managed by Processing Engine"]
    end

    subgraph Storage
        DeltaStorage["Delta Lake (Metadata)"]
        ObjectStorage["Object Storage (S3/GCS/etc) - COGs, Platform Data"]
        ExternalData["External Data Sources (via Marketplace)"]
    end

    %% Connections
    API_GW --> UI_Backend
    API_GW --> MetadataService
    API_GW --> MarketplaceService
    API_GW --> ProcessingEngine

    UI_Backend --> MetadataService
    UI_Backend --> ProcessingEngine
    UI_Backend --> MarketplaceService

    ProcessingEngine --> RayCluster
    ProcessingEngine --> MetadataService
    ProcessingEngine --> ObjectStorage
    ProcessingEngine --> MarketplaceService

    MarketplaceService --> ExternalData
    MarketplaceService --> MetadataService

    MetadataService --> DeltaStorage

    %% Styling for dark mode
    classDef user fill:#2C3E50,stroke:#7F8C8D,stroke-width:1px,color:#ECF0F1
    classDef control fill:#34495E,stroke:#7F8C8D,stroke-width:1px,color:#ECF0F1
    classDef execution fill:#273746,stroke:#7F8C8D,stroke-width:1px,color:#ECF0F1
    classDef storage fill:#873600,stroke:#7F8C8D,stroke-width:1px,color:#ECF0F1

    class Laptop,ManagedNotebook,WorkflowScript user
    class API_GW,UI_Backend,MetadataService,MarketplaceService control
    class ProcessingEngine,RayCluster execution
    class DeltaStorage,ObjectStorage,ExternalData storage
```

The architecture consists of:

1.  **User Environments:** Where users interact via the SDK (Laptop, Hosted Notebooks, Job Scripts).
2.  **Platform Control Plane:** Manages the platform (API Gateway, UI Backend, Metadata Service, Marketplace Service). Runs in the cloud (e.g., K8s).
3.  **Platform Execution Plane:** Performs the actual data processing (Processing Engine Service, optional Ray Cluster). Runs in the cloud.
4.  **Storage:** Underlying storage for metadata (Delta Lake) and data (Object Storage).

The SDK client in the user environment communicates via the API Gateway to the backend services, primarily the Processing Engine, which handles the heavy lifting and streams results back using Arrow Flight.

## Repository Structure

This monorepo contains the different components of the Terrafloww platform:

```
internal-platform-v2/
.
├── docs
│   ├── KT_doc.md
│   ├── repo-structure.md
│   └── schema_organization.md
├── infra
│   ├── docker-compose
│   │   └── docker-compose.yml
│   └── k8s
│       └── readme.md
├── libs
│   ├── tfw_adapters
│   │   ├── pyproject.toml
│   │   └── src
│   │       └── terrafloww
│   │           └── adapters
│   │               ├── cog_source.py
│   │               ├── dicom_source.py
│   │               ├── __init__.py
│   │               └── parquet_source.py
│   ├── tfw_core_utils
│   │   ├── pyproject.toml
│   │   ├── README.md
│   │   └── src
│   │       └── tfw_core_utils
│   │           ├── hash.py
│   │           └── __init__.py
│   ├── tfw_engine_core
│   │   ├── pyproject.toml
│   │   ├── README.md
│   │   └── src
│   │       └── terrafloww
│   │           ├── engine_core
│   │           │   ├── bands
│   │           │   │   ├── __init__.py
│   │           │   │   └── sentinel2.py
│   │           │   ├── catalog_client.py
│   │           │   ├── fetch.py
│   │           │   ├── grid.py
│   │           │   ├── __init__.py
│   │           │   ├── parser.py
│   │           │   ├── process.py
│   │           │   ├── registry.py
│   │           │   ├── runtime_ray
│   │           │   │   ├── common_types.py
│   │           │   │   ├── driver.py
│   │           │   │   ├── __init__.py
│   │           │   │   ├── planner.py
│   │           │   │   └── worker.py
│   │           │   └── utils.py
│   │           └── __init__.py
│   ├── tfw_processing_api
│   │   ├── __init__.py
│   │   ├── pyproject.toml
│   │   └── src
│   │       └── terrafloww
│   │           ├── __init__.py
│   │           └── processing_engine
│   │               ├── __init__.py
│   │               └── v1
│   │                   ├── __init__.py
│   │                   ├── processing_engine_pb2_grpc.py
│   │                   ├── processing_engine_pb2.py
│   │                   └── processing_engine_pb2.pyi
│   ├── tfw_raster_schemas
│   │   ├── pyproject.toml
│   │   ├── README.md
│   │   └── src
│   │       └── tfw_raster_schemas
│   │           ├── datasets.py
│   │           ├── grid.py
│   │           ├── __init__.py
│   │           ├── operations.py
│   │           ├── raster.py
│   │           └── stac.py
│   └── tfw_ray_utils
│       ├── pyproject.toml
│       ├── README.md
│       └── src
│           └── tfw_ray_utils
│               ├── get_geo_datasource.py
│               └── __init__.py
├── misc
│   └── storage
│       ├── hybrid_storage.py
│       └── versioning.py
├── new_tasks.md
├── protos
│   └── terrafloww
│       └── processing_engine
│           └── v1
│               └── processing_engine.proto
├── pyproject.toml
├── README.md
├── scripts
│   ├── cleanup_obsolete_code.sh
│   ├── ingest_ext_stac.py
│   ├── install_dev.sh
│   ├── readme.md
│   └── requirements.txt
# SDK moved to separate repository: terrafloww-sdk-public/
│           └── test_ndvi.py
├── services
│   ├── __init__.py
│   ├── metadata_service
│   │   └── app
│   │       ├── crud
│   │       │   ├── datasets.py
│   │       │   ├── delta_utils.py
│   │       │   ├── grid_templates.py
│   │       │   ├── jobs.py
│   │       │   └── serialization.py
│   │       ├── __init__.py
│   │       ├── models
│   │       │   └── schemas.py
│   │       └── routers
│   │           ├── datasets.py
│   │           ├── grid_templates.py
│   │           └── jobs.py
│   └── processing_engine
│       ├── app
│       │   ├── flight_server.py
│       │   ├── grpc_service.py
│       │   ├── __init__.py
│       │   └── main.py
│       ├── Dockerfile
│       ├── __init__.py
│       └── requirements.txt
└── setup.py

53 directories, 85 files (SDK moved to separate repository)
```

### Key Components

* **libs/**: Reusable libraries that provide core functionality
  * **terrafloww.engine_core**: Optimized COG reading and processing utilities
  * **tfw_raster_schemas**: Arrow schemas for raster data
  * **tfw_processing_api**: Generated protobuf/gRPC code for the Processing Engine API
  * **tfw_ray_utils**: Ray-specific utilities for distributed processing

* **Public SDK**: The client-side SDK is now in a separate repository (terrafloww-sdk-public)
  * Provides a user-friendly API for defining and executing workflows
  * Communicates with the backend services via gRPC and Arrow Flight

* **services/**: Backend microservices that power the platform
  * **processing_engine**: Core service that executes workflows using Ray
  * **metadata_service**: Manages metadata about datasets, workflows, and results
  * **marketplace_service**: Provides data discovery and access capabilities
  * **ui_backend**: Backend for the web UI

* **protos/**: Protocol buffer definitions for service APIs
  * Defines the contract between the SDK and backend services


## 🚀 Quick Start

### Production Deployment (Kubernetes)
```bash
# Deploy entire platform to Kubernetes
./scripts/deploy-platform.sh all

# Test deployment (use public SDK)
cd ../terrafloww-sdk-public && python tests/test_public_sdk.py
```

### Local Development Setup

1.  **Clone:** `git clone <this-repo-url> terrafloww-platform-v2 && cd terrafloww-platform-v2`
2.  **Environment Setup:** Use Python 3.12+ and `uv`.
    ```bash
    uv venv .venv --python 3.12
    source .venv/bin/activate
    ```
3.  **Install Dependencies:** Install libraries and SDK in editable mode.
    ```bash
    ./scripts/install_dev.sh
    ```
4.  **Generate Protobuf/gRPC Code:** (Requires `grpcio-tools`)
    ```bash
    # Run commands from setup steps to generate code in sdk/src and services/processing_engine/app

    PROTO_GEN_OUT_DIR=./libs/tfw_processing_api/src
    python -m grpc_tools.protoc -I=./protos --python_out=${PROTO_GEN_OUT_DIR} --pyi_out=${PROTO_GEN_OUT_DIR} --grpc_python_out=${PROTO_GEN_OUT_DIR} ./protos/terrafloww/processing_engine/v1/processing_engine.proto


    ```
5.  **Run Backend Service Skeleton (When Implemented):**
    ```bash
    # Example (requires implementation in services/processing_engine)
    # cd services/processing_engine
    # python -m app.main
    ```
6.  **Run SDK Examples/Tests:**
    ```bash
    # ingest a few Sentinel‑2 scenes into the Delta catalog
    python scripts/ingest_ext_stac.py https://earth-search.aws.element84.com/v1 sentinel-2-l2a \
    --bbox "-74.05,40.65,-73.85,40.85" \
    --datetime "2024-03-01/2024-03-10" --max-items 10

    # run smoke‑test (Ray locally)
    pytest sdk/src/tests/test_dsl_compute.py -q
    ```


---

## Repository layout (abridged)

| Path                           | Role                                                             |
|--------------------------------|------------------------------------------------------------------|
| `libs/tfw_engine_core`         | COG fetch + decode, window grid, kernel **registry**, Ray runtime |
| `libs/tfw_dsl`                 | Tiny PEG grammar → logical workflow plan                         |
| `terrafloww-sdk-public/`       | Thin client; builds plans & calls Engine gRPC/Flight (separate repo) |
| `services/processing_engine`   | **Planner / Executor ( Ray ) / Flight server**                    |
| `services/metadata_service`    | Delta Lake catalog, job lineage (stubbed)                         |
| `scripts/`                     | Dev helpers – STAC ingestor, benches, env setup                   |
| `docs/`                        | High‑level design notes                                           |

A full tree is kept in `docs/repo-structure.md`.

---
## Getting Started (Developer Onboarding)

### Prerequisites
- Python 3.12+
- virtualenv (uv)
- Docker (for optional services)
- Ray (local or cluster)

### Setup Steps

1. Clone the Repo:
```bash
git clone <repo-url> internal-platform-v2 && cd internal-platform-v2
```

2. Create and Activate Virtual Environment:
```bash
uv venv .venv --python 3.12   # or python3 -m venv .venv
source .venv/bin/activate
```

3. Install Dependencies (Editable Mode):
```bash
./scripts/install_dev.sh
```

4. Generate gRPC/Protobuf Stubs (if protos change):
```bash
PROTO_OUT=./libs/tfw_processing_api/src
python -m grpc_tools.protoc \
  -I=./protos \
  --python_out=${PROTO_OUT} \
  --grpc_python_out=${PROTO_OUT} \
  protos/terrafloww/processing_engine/v1/processing_engine.proto
```

5. Prepare Local Delta Catalog:
```bash
python scripts/ingest_ext_stac.py \
  https://earth-search.aws.element84.com/v1 sentinel-2-l2a \
  --bbox "-74.05,40.65,-73.85,40.85" \
  --datetime "2024-03-01/2024-03-10" \
  --max-items 10
```

### Start Backend Services

1. Processing Engine (gRPC + Flight):
```bash
cd services/processing_engine
python -m app.main   # picks up HOST, GRPC_PORT, FLIGHT_PORT from env
```

2. Metadata Service:
```bash
cd services/metadata_service
uvicorn app.main:app --reload --port 8000
```

### Run Tests

1. Full NDVI Example/Test:
```bash
cd sdk
python src/tests/test_ndvi.py
```

**Expected Output** (Production Ready):
```
Result table shape: 2 rows, 10 columns
result num rows is as expected ✅
ndvi present in result ✅
unique windows are as expected ✅
```

### Tip
Ensure the Processing Engine’s gRPC (default 50051) and Flight (default 50052) ports are reachable and not blocked by a firewall.

## 📚 Documentation

- **[DEPLOYMENT.md](DEPLOYMENT.md)**: Complete deployment guide with troubleshooting
- **[Architecture Details](#high-level-architecture-api-first)**: System architecture and design
- **[Repository Structure](#repository-structure)**: Code organization and components

## 🏗️ Architecture Highlights

### Current Production Architecture
- **Processing Engine**: gRPC + Apache Arrow Flight server with stateful execution
- **Ray Cluster**: Distributed computing with reliable internal networking
- **SDK Client**: Python library with synchronous API (`load`, `apply`, `compute`)
- **Kubernetes Native**: Production deployment with immutable image tags

### Key Technical Achievements
- **Zero Data Loss**: Eliminated race conditions in distributed processing
- **Reliable Networking**: Internal Kubernetes DNS for pod-to-pod communication
- **Scalable Processing**: Ray-based parallel execution with direct Flight uploads
- **Production Ready**: Comprehensive testing and deployment automation

## Contributing

*(Add your contribution guidelines here)*

## License

*(Add your license information here, e.g., Apache-2.0)*

---