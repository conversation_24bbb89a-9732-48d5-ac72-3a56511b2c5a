// protos/terrafloww/processing_engine/v1/processing_engine.proto
syntax = "proto3";

package terrafloww.processing_engine.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// Represents the parameters needed to load initial data
message LoadParameters {
    string collection = 1;
    repeated string bands = 2;
    string aoi_wkt = 3;
    string aoi_crs = 4;
    string datetime_filter = 5;
    google.protobuf.Struct property_filters = 6;
    int32 scene_limit = 7;
    string catalog_identifier = 8;
    int32 spatial_window_limit = 9;  // Add this field
}

// Represents a single apply step
message ApplyFunction {
    string function_id = 1; // e.g., "spectral.calculate_ndvi", "ml.run_pytorch_model"
    google.protobuf.Struct parameters = 2; // Function-specific parameters
}

// Represents the overall workflow plan
message WorkflowPlan {
    LoadParameters load_step = 1;
    repeated ApplyFunction apply_steps = 2;
    int32 head_limit = 3; // Limit on final output chunks (0 means no limit)
}

// Request to execute a workflow
message ExecuteWorkflowRequest {
    WorkflowPlan plan = 1;
    enum ExecutionMode {
        EXECUTION_MODE_UNSPECIFIED = 0;
        SEQUENTIAL = 1;
        RAY = 2;
    }
    ExecutionMode execution_mode = 2;
    string job_id = 3; // Optional ID for tracking
}

// Response from initiating execution
message ExecuteWorkflowResponse {
    string execution_id = 1; // Unique ID for this specific execution run
    string flight_ticket = 2; // Ticket for Arrow Flight DoGet
    string status_message = 3;
}

// Define the main gRPC service
service ProcessingEngineService {
    rpc ExecuteWorkflow (ExecuteWorkflowRequest) returns (ExecuteWorkflowResponse);
}