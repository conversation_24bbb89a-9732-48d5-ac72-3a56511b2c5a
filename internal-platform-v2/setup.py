from setuptools import setup, find_packages
from setuptools.command.build_py import build_py
import subprocess
import os

class BuildPyCommand(build_py):
    """Custom build command to compile protobuf files."""
    def run(self):
        # Compile protobuf files
        proto_dir = os.path.join(os.path.dirname(__file__), 'protos')
        output_dir = os.path.join(os.path.dirname(__file__), 'libs', 'tfw_processing_api', 'src')
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Compile the proto file
        proto_file = os.path.join(proto_dir, 'terrafloww', 'processing_engine', 'v1', 'processing_engine.proto')
        cmd = [
            'python', '-m', 'grpc_tools.protoc',
            f'--python_out={output_dir}',
            f'--pyi_out={output_dir}',
            f'--grpc_python_out={output_dir}',
            f'--proto_path={proto_dir}',
            proto_file
        ]
        subprocess.run(cmd, check=True)
        
        # Run the normal build
        build_py.run(self)

setup(
    name='terrafloww-platform-monorepo',
    version='0.1.0',
    description='Monorepo for the Terrafloww Platform (SDK, Services, Libs)',
    author='Terrafloww Team',
    packages=[],  # No packages in the root
    cmdclass={
        'build_py': BuildPyCommand,
    },
    install_requires=[
        'grpcio>=1.40.0',
        'grpcio-tools>=1.40.0',
        'protobuf>=3.19.0',
    ],
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=23.0.0',
            'mypy>=1.0.0',
            'ruff>=0.1.0',
        ],
    },
    python_requires='>=3.10',
)