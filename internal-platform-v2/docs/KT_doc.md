# High-Level Philosophy

## Lazy, Declarative Pipelines

The SDK lets you build a description of what you want (e.g., "load Sentinel-2, compute NDVI, take first 2 tiles") without pulling data immediately.

This mirrors patterns in Spark or Dask: you declare transformations, then call `.compute()`.

## Separation of Concerns

- SDK: User-facing, constructs pipelines and handles retries/streaming
- gRPC API: Receives workflow plans, kicks off execution
- Ray Driver: Distributes the work, fetches imagery, runs kernels, collates results
- Flight Server: Streams results back to client as Arrow tables

## Columnar, Tile-Based Compute

- Imagery is chopped into small tiles (WindowSpecs)
- Each tile is processed in parallel, producing a PyArrow RecordBatch
- Batches get concatenated into a single `pa.Table` for easy downstream analysis

## In-Memory Caching & Idempotency

- Once a workflow execution_id finishes, its results live in a local cache
- Re-requests with the same execution_id hit the cache (no re-compute)

# Architecture Overview

```mermaid
flowchart LR
    subgraph SDK
      A[collections.py] --> B[loaders.py]
      A --> C[exceptions.py]
    end

    subgraph "Processing Engine"  
      D[grpc_service.py] --> E[driver.RayDriver]
      E --> F[planner.py]
      F --> G[worker.py]
      G --> H[process.py & bands/sentinel2.py]
      E --> I[catalog_client.py]
    end

    subgraph Flight_Server
      J[flight_server.py] --> K[caches & locks]
    end

    SDK -->|gRPC| D
    SDK -->|Flight| J
    E -->|writes| K
    J -->|streams| SDK
```
# SDK Layer

## collections.py

- Core class: `GeoImageCollection`
- Methods like `.apply_func()`, `.head()` build an internal DAG of operations
- `.compute()`:
  - Calls gRPC API to start workflow (ExecuteWorkflow)
  - Polls Flight server via `do_get(ticket)` (with retry/backoff) until results arrive
  - Wraps results into a `pa.Table`

## loaders.py

- Helpers for defining lazy data sources (e.g., STAC-based Sentinel-2)
- Encapsulates how to fetch bands, metadata, AOI defaults, etc.

## exceptions.py

- Custom error types (e.g., timeouts, parsing errors) surfaced to the user

# gRPC API

## grpc_service.py

### Main Functionality
- Entry point for all pipeline executions
- Exposes `ExecuteWorkflow(WorkflowPlan) → ExecuteWorkflowResponse`

### Workflow Process
1. Validates incoming plan (collection name, function IDs, filters, head limits)
2. Instantiates singleton `RayDriver`
3. Calls `start_execution(execution_id, plan)`
4. Immediately returns `execution_id + flight_ticket` for client polling

# Ray Driver & Planner

## driver.py (RayDriver)

### Workflow Lifecycle Management

1. Initialize Ray (once)
2. Consume async generator of WindowSpecs from planner
3. Submit each window as Ray task to `worker.process_batch_on_worker`

Gather the resulting pyarrow.RecordBatch objects.

Concatenate them into one pyarrow.Table.

Write the table into LOCAL_FLIGHT_RESULT_CACHE[execution_id] and set status to COMPLETED.

## planner.py

### Workflow Planning

1. Resolves band aliases via the CatalogClient
2. Queries the STAC catalog for available assets
3. Splits the global AOI into spatial tiles (WindowSpecs) until head_limit is reached
4. Yields each tile spec (with URLs, CRS transforms, etc.) to the driver

## worker.py

### Ray Process Execution

1. Fetches COGs for each band via httpx (partial-range GET)
2. Decodes with rasterio into NumPy arrays
3. Stacks into an Arrow RecordBatch
4. Calls kernel function (e.g., _ndvi_kernel) to compute new arrays
5. Returns RecordBatch with original columns + new outputs

# Processing & Kernels

## process.py

### NDVI Kernel Implementation

```python
def _ndvi_kernel(batch: RecordBatch, red_band: str, nir_band: str) -> RecordBatch:
    # extract arrays, compute (nir - red)/(nir + red), handle divide-by-zero
    # append as new 'ndvi' column
```

## bands/sentinel2.py

- Collection-specific defaults:
  - Maps "red" → "B04"
  - Maps "nir" → "B08"

# Arrow-Flight Server

## Overview

- Runs alongside gRPC server (default port 50052)
- Exposes synchronous and asynchronous methods

## Synchronous Methods

### do_get(ticket)

1. Parses execution_id
2. Creates placeholder status if missing
3. On completion:
   - Retrieves pa.Table from cache
   - Returns RecordBatchStream
4. On failure: Throws error
5. Otherwise: Throws FlightUnavailableError (triggers SDK retry)

## Caching & Locks

```python
LOCAL_FLIGHT_RESULT_CACHE: Dict[str, pa.Table]
LOCAL_FLIGHT_STATUS_CACHE: Dict[str, {"status": ..., "details": ...}]
_FLIGHT_LOCK: Protects async writes
FlightServer._sync_lock: Protects sync reads/writes
```

# End-to-End Flow

## User Code

```python
collection = GeoImageCollection("sentinel-2-l2a")\
               .apply_func("terrafloww.spectral.ndvi")\
               .head(2)
result = collection.compute()
```

## Flow Breakdown

### SDK Layer

- Serializes plan proto
- Makes gRPC call to grpc_service.py
- Starts polling Flight with ticket

### gRPC Service

- Calls RayDriver to start execution

### Ray Execution

#### Planner

- Yields two WindowSpecs

#### Driver

- Fires off two Ray tasks

#### Workers

- Fetch imagery
- Compute NDVI
- Return two RecordBatches

#### Driver

- Concatenates into single pa.Table with two rows

Writes into in-memory cache and marks “COMPLETED.”

### Flight Server

- Each SDK poll hits do_get → sees PENDING until the driver finishes
- Once “COMPLETED,” returns the pa.Table as a RecordBatchStream

Once “COMPLETED,” returns the pa.Table as a RecordBatchStream.

### SDK Layer

- Reads the stream
- Converts to pa.Table
- Returns the final result

### User

Now has a table with two rows and columns:

```text
['chunk_id','raster_data','shape','bounds','crs','datetime','bands','label','quality','ndvi']
```

# Codebase Navigation

1. **SDK Entry Point (Separate Repository)**
   - Start with: `terrafloww-sdk-public/src/terrafloww/workflow.py`
   - This is your main entrypoint (Workflow class)

2. **gRPC Flow**
   - Client stub calls: `services/processing_engine/app/grpc_service.py`

3. **Execution Flow**
   - `runtime_ray/driver.py` → `planner.py` → `worker.py`

4. **Kernel Location**
   - Kernels: `process.py`
   - Band mappers: `bands/` directory

5. **Flight Server**
   - Main server: `services/processing_engine/app/flight_server.py`
   - Caches: Two global dicts in `driver.py`
