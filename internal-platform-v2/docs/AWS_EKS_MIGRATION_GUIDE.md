# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# AWS EKS Migration Guide

## Overview

This guide provides step-by-step instructions for migrating the Terrafloww Platform from DigitalOcean Kubernetes to AWS EKS.

## Migration Strategy

### Option A: Parallel Deployment (Recommended)
1. Setup AWS EKS alongside current DigitalOcean
2. Test and validate on AWS
3. Gradual traffic migration
4. Decommission DigitalOcean

### Option B: Blue-Green Migration
1. Setup complete AWS environment
2. Full cutover during maintenance window
3. Keep DigitalOcean as backup for rollback

## Prerequisites

### AWS Account Setup
```bash
# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS credentials
aws configure
# AWS Access Key ID: YOUR_ACCESS_KEY
# AWS Secret Access Key: YOUR_SECRET_KEY
# Default region name: us-west-2
# Default output format: json

# Verify configuration
aws sts get-caller-identity
```

### Required Tools
```bash
# Install eksctl
curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
sudo mv /tmp/eksctl /usr/local/bin

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Verify installations
aws --version
eksctl version
terraform version
kubectl version --client
```

## Step 1: Infrastructure Setup

### 1.1 Configure Terraform Variables
```bash
cd infra/aws/terraform
cp terraform.tfvars.example terraform.tfvars

# Edit terraform.tfvars with your specific values
vim terraform.tfvars
```

Key variables to customize:
```hcl
aws_region = "us-west-2"
cluster_name = "terrafloww-eks-cluster"
environment = "production"  # or "staging", "dev"

# Node configuration
node_instance_types = ["m5.large"]  # Adjust based on workload
node_desired_size = 3
node_max_size = 6
node_min_size = 1

# Network configuration (adjust if conflicts with existing networks)
vpc_cidr = "10.0.0.0/16"
public_subnet_cidrs = ["********/24", "********/24", "********/24"]
private_subnet_cidrs = ["********/24", "********/24", "********/24"]
```

### 1.2 Deploy AWS Infrastructure
```bash
cd infra/aws/scripts
chmod +x setup-aws-infra.sh
./setup-aws-infra.sh
```

This script will:
- ✅ Check prerequisites and AWS credentials
- ✅ Initialize and plan Terraform deployment
- ✅ Create EKS cluster, VPC, and ECR repositories
- ✅ Configure kubectl for EKS
- ✅ Install KubeRay operator
- ✅ Setup ECR authentication

## Step 2: Application Deployment

### 2.1 Update AWS Secrets
```bash
# Update S3 bucket name for STAC catalog
kubectl patch secret catalog-secrets -n terrafloww-platform \
  -p='{"data":{"bucket":"'$(echo -n "your-s3-bucket-name" | base64)'"}}'

# Update AWS credentials
kubectl patch secret catalog-secrets -n terrafloww-platform \
  -p='{"data":{"access_key_id":"'$(echo -n "YOUR_ACCESS_KEY_ID" | base64)'"}}'

kubectl patch secret catalog-secrets -n terrafloww-platform \
  -p='{"data":{"secret_access_key":"'$(echo -n "YOUR_SECRET_ACCESS_KEY" | base64)'"}}'
```

### 2.2 Deploy Platform to EKS
```bash
cd infra/aws/scripts
chmod +x deploy-to-eks.sh
./deploy-to-eks.sh all
```

This script will:
- ✅ Build and push Ray custom image to ECR
- ✅ Build and push Processing Engine image to ECR
- ✅ Deploy Ray cluster to EKS
- ✅ Deploy Processing Engine to EKS
- ✅ Verify deployment status

## Step 3: Testing and Validation

### 3.1 Basic Connectivity Test
```bash
# Check all pods are running
kubectl get pods -n terrafloww-platform

# Port forward Processing Engine
kubectl port-forward -n terrafloww-platform svc/terrafloww-processing-engine-svc 50051:50051 &

# Port forward Ray Dashboard
kubectl port-forward -n terrafloww-platform svc/terrafloww-ray-cluster-head-svc 8265:8265 &
```

### 3.2 End-to-End Test
```bash
cd ../../../terrafloww-sdk-public
python tests/test_basic.py
```

Expected output:
```
✅ Test completed successfully!
📊 Processed X scenes
📈 Generated Y daily NDVI measurements
```

### 3.3 Performance Validation
```bash
# Run performance test
python tests/monitored_performance_test.py

# Check HTTP optimization is working
kubectl logs -n terrafloww-platform -l app=terrafloww,component=processing-engine | grep "HTTP actor pool"
```

## Step 4: Configuration Differences

### 4.1 Key Differences from DigitalOcean

| Component | DigitalOcean | AWS EKS |
|-----------|--------------|---------|
| Container Registry | `registry.digitalocean.com/terrafloww-dev` | `123456789012.dkr.ecr.us-west-2.amazonaws.com` |
| Authentication | `doctl registry login` | `aws ecr get-login-password` |
| Load Balancer | DigitalOcean LB | AWS ALB/NLB |
| Storage | DigitalOcean Block Storage | EBS |
| Monitoring | DigitalOcean Monitoring | CloudWatch |
| DNS | DigitalOcean DNS | Route 53 |

### 4.2 Environment Variables
AWS-specific environment variables added:
```yaml
env:
- name: AWS_DEFAULT_REGION
  value: "us-west-2"
- name: AWS_REGION
  value: "us-west-2"
- name: TFW_USE_HTTP_POOL
  value: "true"
```

### 4.3 Service Configuration
```yaml
# Internal service DNS for pod-to-pod communication
- name: FLIGHT_INTERNAL_HOST
  value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local"
```

## Step 5: Traffic Migration

### 5.1 Parallel Testing Phase
1. Keep both DigitalOcean and AWS environments running
2. Route test traffic to AWS EKS
3. Compare performance and reliability
4. Validate all features work correctly

### 5.2 Gradual Migration
```bash
# Option 1: DNS-based migration
# Update DNS records to point to AWS LoadBalancer

# Option 2: Application-level migration
# Update SDK configuration to use AWS endpoints
```

### 5.3 Monitoring During Migration
```bash
# Monitor AWS resources
aws cloudwatch get-metric-statistics --namespace AWS/EKS --metric-name cluster.cpu.utilization

# Monitor application logs
kubectl logs -n terrafloww-platform -l app=terrafloww --tail=100 -f

# Monitor Ray cluster
kubectl port-forward -n terrafloww-platform svc/terrafloww-ray-cluster-head-svc 8265:8265
# Visit http://localhost:8265
```

## Step 6: Decommission DigitalOcean

### 6.1 Data Migration
```bash
# Ensure all STAC catalog data is migrated to S3
# Backup any persistent data from DigitalOcean volumes
```

### 6.2 Cleanup DigitalOcean Resources
```bash
# Delete DigitalOcean Kubernetes cluster
doctl kubernetes cluster delete terrafloww-k8s

# Delete DigitalOcean container registry
doctl registry delete terrafloww-dev

# Delete DigitalOcean load balancers, volumes, etc.
```

## Rollback Plan

### Emergency Rollback to DigitalOcean
```bash
# 1. Update DNS to point back to DigitalOcean
# 2. Ensure DigitalOcean cluster is still running
# 3. Verify DigitalOcean services are healthy

# Check DigitalOcean cluster status
doctl kubernetes cluster get terrafloww-k8s

# Switch kubectl context back to DigitalOcean
kubectl config use-context do-nyc1-terrafloww-k8s

# Verify services
kubectl get pods -n terrafloww-platform
```

## Cost Optimization

### 6.1 Right-sizing Instances
```hcl
# Development environment
node_instance_types = ["t3.medium"]  # 2 vCPU, 4 GB RAM
node_desired_size = 2

# Production environment
node_instance_types = ["m5.large"]   # 2 vCPU, 8 GB RAM
node_desired_size = 3
```

### 6.2 Spot Instances (for non-production)
```hcl
# Add to Terraform configuration
capacity_type = "SPOT"
```

### 6.3 Auto-scaling Configuration
```yaml
# Enable cluster autoscaler
enable_cluster_autoscaler = true

# Configure Ray autoscaling
spec:
  enableInTreeAutoscaling: true
  autoscalerOptions:
    upscalingMode: Default
    idleTimeoutSeconds: 60
```

## Troubleshooting

### Common Issues

#### 1. ECR Authentication Failures
```bash
# Re-authenticate with ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com
```

#### 2. EKS Access Issues
```bash
# Update kubeconfig
aws eks update-kubeconfig --region us-west-2 --name terrafloww-eks-cluster

# Check IAM permissions
aws sts get-caller-identity
aws eks describe-cluster --name terrafloww-eks-cluster
```

#### 3. Ray Workers Not Connecting
```bash
# Check Ray cluster status
kubectl get raycluster -n terrafloww-platform
kubectl describe raycluster terrafloww-ray-cluster -n terrafloww-platform

# Check worker logs
kubectl logs -n terrafloww-platform -l ray.io/node-type=worker
```

#### 4. Processing Engine Connection Issues
```bash
# Check service endpoints
kubectl get endpoints -n terrafloww-platform

# Test internal connectivity
kubectl exec -n terrafloww-platform deployment/terrafloww-processing-engine -- nslookup terrafloww-ray-cluster-head-svc
```

## Security Considerations

### 6.1 IAM Roles for Service Accounts (IRSA)
```bash
# Create IAM role for S3 access
aws iam create-role --role-name TerraflowwS3Access --assume-role-policy-document file://trust-policy.json

# Attach S3 policy
aws iam attach-role-policy --role-name TerraflowwS3Access --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess

# Associate with Kubernetes service account
kubectl annotate serviceaccount default -n terrafloww-platform eks.amazonaws.com/role-arn=arn:aws:iam::ACCOUNT:role/TerraflowwS3Access
```

### 6.2 Network Security
```yaml
# Restrict cluster endpoint access
cluster_endpoint_public_access_cidrs = ["YOUR_OFFICE_IP/32"]

# Use private subnets for worker nodes
# Enable VPC flow logs for monitoring
```

### 6.3 Secrets Management
```bash
# Use AWS Secrets Manager (optional)
aws secretsmanager create-secret --name terrafloww/catalog-credentials --secret-string '{"access_key":"...","secret_key":"..."}'
```

## Monitoring and Observability

### 6.1 CloudWatch Integration
```bash
# Enable CloudWatch Container Insights
aws eks update-cluster-config --name terrafloww-eks-cluster --logging '{"enable":["api","audit","authenticator","controllerManager","scheduler"]}'
```

### 6.2 Application Monitoring
```bash
# View application logs
aws logs describe-log-groups --log-group-name-prefix /aws/eks/terrafloww

# Monitor resource usage
kubectl top nodes
kubectl top pods -n terrafloww-platform
```

## Next Steps

1. **Performance Optimization**: Tune instance types and autoscaling based on actual usage
2. **Cost Monitoring**: Set up AWS Cost Explorer and billing alerts
3. **Backup Strategy**: Implement automated backups for STAC catalog and configurations
4. **Disaster Recovery**: Document and test disaster recovery procedures
5. **Security Hardening**: Implement additional security measures like Pod Security Standards

## Support

For issues during migration:
1. Check the troubleshooting section above
2. Review AWS EKS documentation
3. Check Terrafloww platform logs
4. Contact the platform team

---

**Migration Checklist:**
- [ ] AWS account and credentials configured
- [ ] Terraform infrastructure deployed
- [ ] EKS cluster accessible via kubectl
- [ ] ECR repositories created and accessible
- [ ] KubeRay operator installed
- [ ] AWS secrets configured
- [ ] Platform deployed to EKS
- [ ] End-to-end tests passing
- [ ] Performance validation completed
- [ ] Monitoring and alerting configured
- [ ] Rollback plan tested
- [ ] DigitalOcean resources decommissioned
