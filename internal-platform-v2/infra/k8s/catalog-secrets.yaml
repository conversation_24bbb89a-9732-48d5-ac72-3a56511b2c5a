# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: v1
kind: Secret
metadata:
  name: terrafloww-catalog-secrets
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: catalog
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  # To encode: echo -n "your-value" | base64
  # AWS keys
  bucket: c2F0LW91dHB1dHM=  # sat-outputs
  endpoint: aHR0cHM6Ly9zMy5hcC1zb3V0aC0xLmFtYXpvbmF3cy5jb20=  # https://s3.ap-south-1.amazonaws.com
  region: YXAtc291dGgtMQ== # ap-south-1
  access_key_id: ****************************
  secret_access_key: emdOblpEbFZBTXBZT3VOL2FlRDdTc2hUWWc1dzM2OExkTHQxMFZGVQ==
---
# ConfigMap for non-sensitive catalog configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: terrafloww-catalog-config
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: catalog
data:
  # Catalog configuration
  catalog_table_name: "ext_stac_datasets"
  catalog_path_prefix: "catalog"
  storage_backend: "s3"
  
  # Retry and timeout configuration
  s3_retry_attempts: "3"
  s3_timeout_seconds: "30"
  catalog_query_timeout: "60"
