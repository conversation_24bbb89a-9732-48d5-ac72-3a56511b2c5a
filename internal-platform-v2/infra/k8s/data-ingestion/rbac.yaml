# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: v1
kind: ServiceAccount
metadata:
  name: data-ingestion
  namespace: terrafloww-platform
  labels:
    app: data-ingestion

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: terrafloww-platform
  name: data-ingestion-role
  labels:
    app: data-ingestion
rules:
# Allow reading secrets and configmaps
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
# Allow creating and managing jobs (for future API service)
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create", "delete"]
# Allow reading pods (for job monitoring)
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: data-ingestion-binding
  namespace: terrafloww-platform
  labels:
    app: data-ingestion
subjects:
- kind: ServiceAccount
  name: data-ingestion
  namespace: terrafloww-platform
roleRef:
  kind: Role
  name: data-ingestion-role
  apiGroup: rbac.authorization.k8s.io
