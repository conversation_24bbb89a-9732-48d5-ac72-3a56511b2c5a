# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: v1
kind: Service
metadata:
  name: terrafloww-processing-engine-svc
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine
spec:
  type: ClusterIP
  selector:
    app: terrafloww
    component: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP

---
# LoadBalancer service for external access (for testing)
apiVersion: v1
kind: Service
metadata:
  name: terrafloww-processing-engine-lb
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine
    service-type: loadbalancer
spec:
  type: LoadBalancer
  selector:
    app: terrafloww
    component: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP
