version: '3.8'

services:
  processing-engine:
    build:
      context: ../../services/processing_engine
      dockerfile: Dockerfile
    ports:
      - "8000:8000"  # FastAPI
      - "50051:50051"  # gRPC
      - "8815:8815"  # Arrow Flight
    environment:
      - PYTHONPATH=/app
      - GRPC_PORT=50051
      - FLIGHT_PORT=8815
    volumes:
      - ../../services/processing_engine:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  metadata-service:
    build:
      context: ../../services/metadata_service
      dockerfile: Dockerfile
    ports:
      - "8001:8000"  # FastAPI
    environment:
      - PYTHONPATH=/app
    volumes:
      - ../../services/metadata_service:/app
    # Uncomment when metadata service is implemented
    # command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  ui-backend:
    build:
      context: ../../services/ui_backend
      dockerfile: Dockerfile
    ports:
      - "8002:8000"  # FastAPI
    environment:
      - PYTHONPATH=/app
      - PROCESSING_ENGINE_URL=http://processing-engine:8000
      - METADATA_SERVICE_URL=http://metadata-service:8000
    volumes:
      - ../../services/ui_backend:/app
    # Uncomment when ui backend is implemented
    # command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    depends_on:
      - processing-engine
      - metadata-service
