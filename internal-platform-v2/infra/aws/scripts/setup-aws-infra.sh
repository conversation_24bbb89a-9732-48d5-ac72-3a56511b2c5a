#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Setup AWS infrastructure for Terrafloww Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$SCRIPT_DIR/../terraform"
AWS_REGION="${AWS_REGION:-us-west-2}"
CLUSTER_NAME="${CLUSTER_NAME:-terrafloww-eks-cluster}"

echo -e "${BLUE}🚀 Terrafloww AWS Infrastructure Setup${NC}"
echo "========================================"
echo -e "Region: ${GREEN}$AWS_REGION${NC}"
echo -e "Cluster: ${GREEN}$CLUSTER_NAME${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! command_exists aws; then
        missing_tools+=("aws-cli")
    fi
    
    if ! command_exists terraform; then
        missing_tools+=("terraform")
    fi
    
    if ! command_exists kubectl; then
        missing_tools+=("kubectl")
    fi
    
    if ! command_exists eksctl; then
        missing_tools+=("eksctl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        echo ""
        echo "Please install the missing tools:"
        echo "  AWS CLI: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        echo "  Terraform: https://learn.hashicorp.com/tutorials/terraform/install-cli"
        echo "  kubectl: https://kubernetes.io/docs/tasks/tools/"
        echo "  eksctl: https://eksctl.io/introduction/#installation"
        exit 1
    fi
    
    print_success "All required tools are installed"
}

# Check AWS credentials
check_aws_credentials() {
    print_status "Checking AWS credentials..."
    
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS credentials not configured or invalid"
        echo ""
        echo "Please configure AWS credentials:"
        echo "  aws configure"
        echo ""
        echo "Or set environment variables:"
        echo "  export AWS_ACCESS_KEY_ID=your-access-key"
        echo "  export AWS_SECRET_ACCESS_KEY=your-secret-key"
        echo "  export AWS_DEFAULT_REGION=$AWS_REGION"
        exit 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local user_arn=$(aws sts get-caller-identity --query Arn --output text)
    
    print_success "AWS credentials configured"
    echo -e "  Account ID: ${GREEN}$account_id${NC}"
    echo -e "  User/Role: ${GREEN}$user_arn${NC}"
}

# Initialize Terraform
init_terraform() {
    print_status "Initializing Terraform..."
    
    cd "$TERRAFORM_DIR"
    
    # Check if terraform.tfvars exists
    if [ ! -f "terraform.tfvars" ]; then
        print_warning "terraform.tfvars not found, copying from example"
        cp terraform.tfvars.example terraform.tfvars
        echo ""
        echo -e "${YELLOW}Please review and customize terraform.tfvars before proceeding${NC}"
        echo "File location: $TERRAFORM_DIR/terraform.tfvars"
        echo ""
        read -p "Press Enter to continue after reviewing terraform.tfvars..."
    fi
    
    # Initialize Terraform
    terraform init
    
    print_success "Terraform initialized"
}

# Plan Terraform deployment
plan_terraform() {
    print_status "Planning Terraform deployment..."
    
    cd "$TERRAFORM_DIR"
    
    terraform plan -out=tfplan
    
    echo ""
    echo -e "${YELLOW}Review the Terraform plan above${NC}"
    echo "This will create:"
    echo "  - EKS cluster and node group"
    echo "  - VPC with public/private subnets"
    echo "  - ECR repositories"
    echo "  - IAM roles and policies"
    echo ""
    read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Deployment cancelled by user"
        exit 0
    fi
}

# Apply Terraform deployment
apply_terraform() {
    print_status "Applying Terraform deployment..."
    
    cd "$TERRAFORM_DIR"
    
    terraform apply tfplan
    
    print_success "Infrastructure deployed successfully"
}

# Configure kubectl
configure_kubectl() {
    print_status "Configuring kubectl..."
    
    aws eks --region "$AWS_REGION" update-kubeconfig --name "$CLUSTER_NAME"
    
    # Test connection
    if kubectl cluster-info >/dev/null 2>&1; then
        print_success "kubectl configured successfully"
        kubectl get nodes
    else
        print_error "Failed to connect to EKS cluster"
        exit 1
    fi
}

# Install KubeRay operator
install_kuberay() {
    print_status "Installing KubeRay operator..."
    
    # Add KubeRay Helm repository
    helm repo add kuberay https://ray-project.github.io/kuberay-helm/
    helm repo update
    
    # Create namespace
    kubectl create namespace kuberay-operator --dry-run=client -o yaml | kubectl apply -f -
    
    # Install KubeRay operator
    helm upgrade --install kuberay-operator kuberay/kuberay-operator \
        --namespace kuberay-operator \
        --version 1.0.0
    
    # Wait for operator to be ready
    kubectl wait --for=condition=available --timeout=300s deployment/kuberay-operator -n kuberay-operator
    
    print_success "KubeRay operator installed"
}

# Create ECR repositories and get login
setup_ecr() {
    print_status "Setting up ECR repositories..."
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local ecr_registry="${account_id}.dkr.ecr.${AWS_REGION}.amazonaws.com"
    
    # ECR login
    aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ecr_registry"
    
    print_success "ECR configured and logged in"
    echo -e "  Registry: ${GREEN}$ecr_registry${NC}"
    echo -e "  Ray Custom: ${GREEN}$ecr_registry/terrafloww/ray-custom${NC}"
    echo -e "  Processing Engine: ${GREEN}$ecr_registry/terrafloww/processing-engine${NC}"
}

# Display next steps
display_next_steps() {
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local ecr_registry="${account_id}.dkr.ecr.${AWS_REGION}.amazonaws.com"
    
    echo ""
    echo -e "${GREEN}🎉 AWS Infrastructure Setup Complete!${NC}"
    echo "========================================"
    echo ""
    echo "Next steps:"
    echo ""
    echo "1. Build and push Docker images:"
    echo -e "   ${BLUE}cd ../../../${NC}"
    echo -e "   ${BLUE}./infra/aws/scripts/build-and-push-images.sh${NC}"
    echo ""
    echo "2. Deploy Terrafloww platform:"
    echo -e "   ${BLUE}./infra/aws/scripts/deploy-to-eks.sh all${NC}"
    echo ""
    echo "3. Test the deployment:"
    echo -e "   ${BLUE}kubectl get pods -n terrafloww-platform${NC}"
    echo ""
    echo "Useful commands:"
    echo -e "   ${BLUE}kubectl get nodes${NC}"
    echo -e "   ${BLUE}kubectl cluster-info${NC}"
    echo -e "   ${BLUE}aws eks describe-cluster --name $CLUSTER_NAME${NC}"
    echo ""
    echo "ECR Registry: $ecr_registry"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    check_aws_credentials
    init_terraform
    plan_terraform
    apply_terraform
    configure_kubectl
    install_kuberay
    setup_ecr
    display_next_steps
}

# Run main function
main "$@"
