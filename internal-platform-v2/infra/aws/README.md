# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# AWS EKS Migration Infrastructure

## Overview

This directory contains all AWS-specific infrastructure configurations and scripts for migrating the Terrafloww Platform from DigitalOcean to AWS EKS.

## Directory Structure

```
infra/aws/
├── README.md                 # This file
├── terraform/               # Terraform infrastructure as code
│   ├── main.tf              # Main Terraform configuration
│   ├── variables.tf         # Input variables
│   ├── outputs.tf           # Output values
│   └── terraform.tfvars     # Variable values
├── k8s/                     # Kubernetes manifests for AWS
│   ├── eks-cluster.yaml     # EKS cluster configuration
│   ├── ray-cluster-aws.yaml # Ray cluster for EKS
│   ├── processing-engine-aws.yaml # Processing engine for EKS
│   └── aws-secrets.yaml     # AWS-specific secrets
└── scripts/                 # AWS deployment scripts
    ├── setup-aws-infra.sh   # Setup AWS infrastructure
    ├── deploy-to-eks.sh     # Deploy platform to EKS
    └── cleanup-aws.sh       # Cleanup AWS resources
```

## Prerequisites

### AWS CLI and Tools
```bash
# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install eksctl
curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
sudo mv /tmp/eksctl /usr/local/bin

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/
```

### AWS Configuration
```bash
# Configure AWS credentials
aws configure
# AWS Access Key ID: YOUR_ACCESS_KEY
# AWS Secret Access Key: YOUR_SECRET_KEY
# Default region name: us-west-2
# Default output format: json

# Verify configuration
aws sts get-caller-identity
```

## Quick Start

### 1. Setup AWS Infrastructure
```bash
cd infra/aws/scripts
./setup-aws-infra.sh
```

### 2. Deploy Platform to EKS
```bash
./deploy-to-eks.sh all
```

### 3. Test Deployment
```bash
# Port forward and test
kubectl port-forward -n terrafloww-platform svc/terrafloww-processing-engine-svc 50051:50051
cd ../../../terrafloww-sdk-public
python tests/test_basic.py
```

## Configuration

### AWS Region and Account
- **Default Region**: us-west-2
- **Account ID**: Will be auto-detected
- **Cluster Name**: terrafloww-eks-cluster

### Resource Naming Convention
- **EKS Cluster**: terrafloww-eks-cluster
- **ECR Repository**: terrafloww/ray-custom, terrafloww/processing-engine
- **IAM Roles**: terrafloww-eks-*, terrafloww-node-group-*
- **VPC**: terrafloww-vpc

### Cost Estimation
- **EKS Control Plane**: ~$73/month
- **Worker Nodes**: 3x m5.large (~$105/month)
- **ECR Storage**: ~$5/month
- **Total Estimated**: ~$183/month

## Migration from DigitalOcean

### Differences from DigitalOcean Deployment
| Component | DigitalOcean | AWS EKS |
|-----------|--------------|---------|
| Container Registry | registry.digitalocean.com | ECR (************.dkr.ecr.us-west-2.amazonaws.com) |
| Authentication | doctl registry login | aws ecr get-login-password |
| Cluster Management | DigitalOcean Kubernetes | EKS |
| Load Balancer | DigitalOcean LB | AWS ALB/NLB |
| Storage | DigitalOcean Block Storage | EBS |
| Monitoring | DigitalOcean Monitoring | CloudWatch |

### Migration Steps
1. **Setup AWS Infrastructure** (Terraform)
2. **Create ECR Repositories** and push images
3. **Deploy KubeRay Operator** to EKS
4. **Deploy Ray Cluster** with AWS-specific configs
5. **Deploy Processing Engine** with AWS-specific configs
6. **Test and Validate** functionality
7. **Update DNS/Routing** (if applicable)
8. **Decommission DigitalOcean** resources

## Troubleshooting

### Common Issues
- **ECR Authentication**: Ensure AWS CLI is configured correctly
- **EKS Access**: Check IAM permissions for EKS cluster access
- **Node Group Issues**: Verify instance types and availability zones
- **Networking**: Ensure security groups allow required traffic

### Useful Commands
```bash
# Check EKS cluster status
aws eks describe-cluster --name terrafloww-eks-cluster

# Update kubeconfig
aws eks update-kubeconfig --region us-west-2 --name terrafloww-eks-cluster

# Check ECR repositories
aws ecr describe-repositories

# View CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix /aws/eks/terrafloww
```

## Security Considerations

- **IAM Roles**: Least privilege principle
- **Network Security**: Private subnets for worker nodes
- **Secrets Management**: AWS Secrets Manager integration
- **Image Security**: ECR vulnerability scanning enabled

## Next Steps

1. Review and customize Terraform variables in `terraform/terraform.tfvars`
2. Run `terraform plan` to review infrastructure changes
3. Execute `terraform apply` to create AWS resources
4. Deploy platform using `scripts/deploy-to-eks.sh`
5. Test and validate the deployment

For detailed deployment instructions, see the individual script files in the `scripts/` directory.
