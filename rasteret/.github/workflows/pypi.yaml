name: Publish to PyPI

on:
  push:
    tags:
      - 'v*.*.*'

jobs:
  pypi-publish:
    name: upload release to PyPI
    runs-on: ubuntu-latest
    environment: 
        name: pypi
        url : https://pypi.org/project/rasteret
    permissions:
      id-token: write 

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Build package
      run: |
        python setup.py sdist bdist_wheel

    - name: Publish package distributions to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1