# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for COG header parsing functionality.

This script tests that the COG parser module loads correctly and has the right interface.
"""

import asyncio
import logging

from data_marketplace.cog import AsyncCOGHeaderParser, parse_cog_header_info, CogMetadata

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_cog_parser():
    """Test the COG parser module interface."""

    logger.info("🧪 Testing COG Header Parser Module")
    logger.info("=" * 50)

    try:
        # Test that we can import and instantiate the parser
        logger.info("Testing COG parser instantiation...")
        async with AsyncCOGHeaderParser(max_concurrent=10) as parser:
            logger.info("✅ COG parser instantiated successfully!")
            logger.info(f"  Max concurrent: {parser.max_concurrent}")
            logger.info(f"  Batch size: {parser.batch_size}")
            logger.info(f"  Retry attempts: {parser.retry_attempts}")

        # Test CogMetadata type
        logger.info("\nTesting CogMetadata type...")
        test_metadata = CogMetadata(
            width=1024,
            height=1024,
            tile_width=512,
            tile_height=512,
            dtype="uint16",
            crs=32633,
            transform=(10.0, 0.0, 600000.0, 0.0, -10.0, 4000000.0)
        )

        metadata_dict = test_metadata.to_dict()
        logger.info("✅ CogMetadata type works correctly!")
        logger.info(f"  Sample fields: {list(metadata_dict.keys())[:5]}...")

        logger.info("\n✅ All COG parser module tests passed!")
        logger.info("🎉 COG parser is ready for real STAC ingestion!")
        return True

    except Exception as e:
        logger.error(f"❌ COG parser test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = asyncio.run(test_cog_parser())
    if success:
        print("\n🎉 COG parser test completed successfully!")
        print("✅ Real COG header parsing is working correctly")
    else:
        print("\n❌ COG parser test failed!")
        exit(1)
