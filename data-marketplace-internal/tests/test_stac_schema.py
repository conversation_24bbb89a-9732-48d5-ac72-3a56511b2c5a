# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Tests for STAC schema design and flattening."""

import pytest
from datetime import datetime
import pyarrow as pa
from data_marketplace.ingestion.stac_schema import (
    StacTableSchema, CogHeadersSchema, StacSchemaConfig
)


class TestStacTableSchema:
    """Test STAC table schema functionality."""
    
    def test_schema_creation(self):
        """Test basic schema creation."""
        config = StacSchemaConfig()
        schema = StacTableSchema(config)
        
        pa_schema = schema.get_core_schema()
        
        # Check required fields exist
        field_names = [field.name for field in pa_schema]
        required_fields = [
            "scene_id", "collection", "datetime", "geometry", "bbox",
            "s2_cell_id", "year", "month"
        ]
        
        for field in required_fields:
            assert field in field_names, f"Missing required field: {field}"
        
        # Check data types
        assert pa_schema.field("scene_id").type == pa.string()
        assert pa_schema.field("s2_cell_id").type == pa.uint64()
        assert pa_schema.field("year").type == pa.int32()
    
    def test_optimization_settings(self):
        """Test optimization column configurations."""
        schema = StacTableSchema()
        
        bloom_columns = schema.get_bloom_filter_columns()
        dict_columns = schema.get_dictionary_columns()
        partition_columns = schema.get_partition_columns()
        
        # Verify key columns are included
        assert "scene_id" in bloom_columns
        assert "collection" in bloom_columns
        assert "s2_cell_id" in bloom_columns
        
        assert "collection" in dict_columns
        assert "platform" in dict_columns
        
        assert partition_columns == ["year", "month"]
    
    def test_stac_item_flattening(self):
        """Test STAC item flattening functionality."""
        schema = StacTableSchema()
        
        # Sample STAC item
        stac_item = {
            "id": "test_scene_001",
            "collection": "test-collection",
            "stac_version": "1.0.0",
            "geometry": {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
            },
            "bbox": [0, 0, 1, 1],
            "properties": {
                "datetime": "2025-01-15T10:00:00Z",
                "platform": "test-sat",
                "instruments": ["test-sensor"],
                "constellation": "test-constellation",
                "gsd": 10.0,
                "eo:cloud_cover": 15.5,
                "processing:level": "L2A"
            },
            "assets": {
                "B01": {"href": "s3://bucket/B01.tif", "type": "image/tiff"},
                "B02": {"href": "s3://bucket/B02.tif", "type": "image/tiff"},
                "thumbnail": {"href": "s3://bucket/thumb.jpg", "type": "image/jpeg"}
            }
        }
        
        # Flatten the item
        flattened = schema.flatten_stac_item(stac_item)
        
        # Verify core fields
        assert flattened["scene_id"] == "test_scene_001"
        assert flattened["collection"] == "test-collection"
        assert flattened["platform"] == "test-sat"
        assert flattened["gsd"] == 10.0
        assert flattened["cloud_cover"] == 15.5
        
        # Verify temporal fields
        assert flattened["year"] == 2025
        assert flattened["month"] == 1
        assert flattened["day"] == 15
        
        # Verify asset processing
        assert flattened["asset_count"] == 3
        assert flattened["cog_count"] == 2  # Only TIFF files
        assert "B01" in flattened["cog_keys"]
        assert "B02" in flattened["cog_keys"]
        assert "thumbnail" not in flattened["cog_keys"]
        
        # Verify spatial indexing (if enabled)
        if schema.config.include_s2_indexing:
            assert flattened["s2_cell_id"] is not None
            assert flattened["s2_level"] is not None
            assert isinstance(flattened["s2_cells"], list)
    
    def test_record_validation(self):
        """Test record validation functionality."""
        schema = StacTableSchema()
        
        # Valid record
        valid_record = {
            "scene_id": "test_001",
            "collection": "test",
            "datetime": datetime.now(),
            "year": 2025,
            "month": 1
        }
        
        assert schema.validate_record(valid_record) == True
        
        # Invalid records
        invalid_records = [
            {},  # Empty record
            {"scene_id": "test"},  # Missing required fields
            {"scene_id": "test", "collection": "test", "datetime": "invalid", "year": 2025, "month": 1},  # Invalid datetime
            {"scene_id": "test", "collection": "test", "datetime": datetime.now(), "year": 2025, "month": 13},  # Invalid month
        ]
        
        for invalid_record in invalid_records:
            assert schema.validate_record(invalid_record) == False
    
    def test_s2_indexing_disabled(self):
        """Test schema with S2 indexing disabled."""
        config = StacSchemaConfig(include_s2_indexing=False)
        schema = StacTableSchema(config)
        
        stac_item = {
            "id": "test_no_s2",
            "collection": "test",
            "geometry": {"type": "Point", "coordinates": [0, 0]},
            "bbox": [0, 0, 0, 0],
            "properties": {"datetime": "2025-01-15T10:00:00Z"},
            "assets": {}
        }
        
        flattened = schema.flatten_stac_item(stac_item)
        
        # S2 fields should be None or empty
        assert flattened.get("s2_cell_id") is None
        assert flattened.get("s2_level") is None
        assert flattened.get("s2_cells") == []


class TestCogHeadersSchema:
    """Test COG headers schema functionality."""
    
    def test_cog_schema_creation(self):
        """Test COG headers schema creation."""
        schema = CogHeadersSchema()
        pa_schema = schema.get_schema()
        
        # Check required fields
        field_names = [field.name for field in pa_schema]
        required_fields = [
            "scene_id", "asset_key", "cog_href", "width", "height",
            "band_count", "year", "month"
        ]
        
        for field in required_fields:
            assert field in field_names, f"Missing required field: {field}"
        
        # Check data types
        assert pa_schema.field("scene_id").type == pa.string()
        assert pa_schema.field("width").type == pa.int32()
        assert pa_schema.field("file_size").type == pa.int64()
        assert pa_schema.field("has_overviews").type == pa.bool_()
    
    def test_cog_optimization_settings(self):
        """Test COG schema optimization settings."""
        schema = CogHeadersSchema()
        
        bloom_columns = schema.get_bloom_filter_columns()
        dict_columns = schema.get_dictionary_columns()
        partition_columns = schema.get_partition_columns()
        
        # Verify key columns
        assert "scene_id" in bloom_columns
        assert "asset_key" in bloom_columns
        assert "cog_href" in bloom_columns
        
        assert "asset_key" in dict_columns
        assert "data_type" in dict_columns
        assert "compression" in dict_columns
        
        assert partition_columns == ["year", "month"]


class TestSchemaIntegration:
    """Test integration between STAC and COG schemas."""
    
    def test_schema_compatibility(self):
        """Test that STAC and COG schemas are compatible."""
        stac_schema = StacTableSchema()
        cog_schema = CogHeadersSchema()
        
        # Both should have same partition columns
        assert stac_schema.get_partition_columns() == cog_schema.get_partition_columns()
        
        # Both should have scene_id field with same type
        stac_pa_schema = stac_schema.get_core_schema()
        cog_pa_schema = cog_schema.get_schema()
        
        stac_scene_id = stac_pa_schema.field("scene_id")
        cog_scene_id = cog_pa_schema.field("scene_id")
        
        assert stac_scene_id.type == cog_scene_id.type
        assert stac_scene_id.nullable == cog_scene_id.nullable
    
    def test_end_to_end_processing(self):
        """Test complete STAC item to table record processing."""
        stac_schema = StacTableSchema()
        
        # Complex STAC item with multiple assets
        stac_item = {
            "id": "S2A_MSIL2A_20250115T103321_N0511_R108_T32TQM_20250115T134649",
            "collection": "sentinel-2-l2a",
            "stac_version": "1.0.0",
            "geometry": {
                "type": "Polygon",
                "coordinates": [[[2.999755859375, 50.958427734375],
                               [4.000244140625, 50.958427734375], 
                               [4.000244140625, 51.958572265625],
                               [2.999755859375, 51.958572265625],
                               [2.999755859375, 50.958427734375]]]
            },
            "bbox": [2.999755859375, 50.958427734375, 4.000244140625, 51.958572265625],
            "properties": {
                "datetime": "2025-01-15T10:33:21.024000Z",
                "platform": "sentinel-2a",
                "instruments": ["msi"],
                "constellation": "sentinel-2",
                "mission": "sentinel-2",
                "gsd": 10.0,
                "eo:cloud_cover": 23.45,
                "processing:level": "L2A",
                "product_type": "S2MSI2A",
                "view:sun_azimuth": 158.123,
                "view:sun_elevation": 23.456,
            },
            "assets": {
                "B01": {"href": "s3://bucket/B01.tif", "type": "image/tiff"},
                "B02": {"href": "s3://bucket/B02.tif", "type": "image/tiff"},
                "B03": {"href": "s3://bucket/B03.tif", "type": "image/tiff"},
                "B04": {"href": "s3://bucket/B04.tif", "type": "image/tiff"},
                "thumbnail": {"href": "s3://bucket/thumbnail.jpg", "type": "image/jpeg"}
            }
        }
        
        # Process the item
        flattened = stac_schema.flatten_stac_item(stac_item)
        
        # Validate the result
        assert stac_schema.validate_record(flattened)
        
        # Verify all expected fields are present and correct
        assert flattened["scene_id"].startswith("S2A_MSIL2A")
        assert flattened["collection"] == "sentinel-2-l2a"
        assert flattened["platform"] == "sentinel-2a"
        assert flattened["constellation"] == "sentinel-2"
        assert flattened["gsd"] == 10.0
        assert flattened["cloud_cover"] == 23.45
        assert flattened["processing_level"] == "L2A"
        
        # Verify temporal partitioning
        assert flattened["year"] == 2025
        assert flattened["month"] == 1
        assert flattened["day"] == 15
        
        # Verify asset processing
        assert flattened["asset_count"] == 5
        assert flattened["cog_count"] == 4  # 4 TIFF files
        assert len(flattened["cog_keys"]) == 4
        
        # Verify spatial indexing
        if stac_schema.config.include_s2_indexing:
            assert flattened["s2_cell_id"] is not None
            assert isinstance(flattened["s2_cells"], list)
            assert len(flattened["s2_cells"]) > 0
        
        # Verify bbox structure
        bbox = flattened["bbox"]
        assert hasattr(bbox, 'xmin')
        assert hasattr(bbox, 'ymin') 
        assert hasattr(bbox, 'xmax')
        assert hasattr(bbox, 'ymax')
        assert bbox.xmin < bbox.xmax
        assert bbox.ymin < bbox.ymax
