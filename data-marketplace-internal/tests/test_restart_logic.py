#!/usr/bin/env python3
"""
Local testing script for the new restart logic.
Run this to validate the counting and restart logic before deploying to AWS.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
import pystac_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_stac_api_counts():
    """Test STAC API counting for different date ranges."""
    logger.info("🧪 Testing STAC API counting...")
    
    try:
        # Connect to Earth Search API
        catalog = pystac_client.Client.open("https://earth-search.aws.element84.com/v1")
        
        # Test different date ranges
        test_dates = [
            "2025-01-01/2025-01-02",  # Single day
            "2025-01-01/2025-01-03",  # Two days
            "2025-01-01/2025-01-08",  # One week
        ]
        
        for date_range in test_dates:
            logger.info(f"📅 Testing date range: {date_range}")
            
            search_params = {
                "collections": ["sentinel-2-l2a"],
                "datetime": date_range,
                "limit": 1
            }
            
            search = catalog.search(**search_params)
            count = search.matched()
            
            if count is None:
                logger.warning(f"❌ API doesn't support count for {date_range}")
            else:
                logger.info(f"✅ Count for {date_range}: {count}")
                
                # Validate the count makes sense
                if count > 50000:  # Sanity check
                    logger.warning(f"⚠️  Unusually high count: {count}")
                elif count == 0:
                    logger.info(f"ℹ️  No data for {date_range}")
                else:
                    logger.info(f"✅ Reasonable count: {count}")
    
    except Exception as e:
        logger.error(f"❌ STAC API test failed: {e}")

def test_date_parsing():
    """Test date parsing and iteration logic."""
    logger.info("🧪 Testing date parsing...")
    
    try:
        # Test date range parsing
        datetime_range = "2025-01-01/2025-01-05"
        start_date_str, end_date_str = datetime_range.split("/")
        start_date = datetime.fromisoformat(start_date_str.replace('Z', '+00:00')).date()
        end_date = datetime.fromisoformat(end_date_str.replace('Z', '+00:00')).date()
        
        logger.info(f"📅 Parsed range: {start_date} to {end_date}")
        
        # Test date iteration
        current_date = start_date
        dates_checked = []
        while current_date <= end_date:
            dates_checked.append(current_date.isoformat())
            current_date = current_date + timedelta(days=1)
        
        logger.info(f"✅ Dates to check: {dates_checked}")
        
        if len(dates_checked) == 5:  # Should be 5 days
            logger.info("✅ Date iteration working correctly")
        else:
            logger.error(f"❌ Expected 5 dates, got {len(dates_checked)}")
    
    except Exception as e:
        logger.error(f"❌ Date parsing test failed: {e}")

def test_delta_table_simulation():
    """Simulate Delta table operations without actual S3."""
    logger.info("🧪 Testing Delta table simulation...")
    
    # Simulate file statistics
    mock_stats = [
        {"numRecords": 1000, "minValues": {"datetime": "2025-01-01"}, "maxValues": {"datetime": "2025-01-01"}},
        {"numRecords": 1200, "minValues": {"datetime": "2025-01-02"}, "maxValues": {"datetime": "2025-01-02"}},
        {"numRecords": 800, "minValues": {"datetime": "2025-01-03"}, "maxValues": {"datetime": "2025-01-03"}},
    ]
    
    total_records = sum(stat["numRecords"] for stat in mock_stats)
    logger.info(f"✅ Mock total records: {total_records}")
    
    # Test daily estimation
    estimated_daily = max(1, total_records // 90)  # 3 months estimation
    logger.info(f"✅ Estimated daily count: {estimated_daily}")

async def run_all_tests():
    """Run all local tests."""
    logger.info("🚀 Starting local tests for restart logic...")
    logger.info("=" * 60)
    
    # Test 1: Date parsing
    test_date_parsing()
    logger.info("-" * 40)
    
    # Test 2: STAC API counts
    await test_stac_api_counts()
    logger.info("-" * 40)
    
    # Test 3: Delta table simulation
    test_delta_table_simulation()
    logger.info("-" * 40)
    
    logger.info("✅ All local tests completed!")
    logger.info("🚀 Ready to test on AWS with real data")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
