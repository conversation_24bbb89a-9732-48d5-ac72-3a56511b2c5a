# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Tests for spatial utilities."""

import pytest
import tempfile
import os
import json
import subprocess
import urllib.request
from pathlib import Path
from shapely.geometry import box, Polygon, Point
import pyarrow as pa
import pyarrow.parquet as pq
import geoarrow.pyarrow as ga
from data_marketplace.spatial.s2_utils import S2Utils
from data_marketplace.spatial.bbox_utils import BboxUtils, BboxStruct
from data_marketplace.spatial.geoarrow_utils import GeoArrowUtils
from data_marketplace.spatial.spatial_indexer import SpatialIndexer, SpatialIndexConfig


class TestS2Utils:
    """Test S2 spatial indexing utilities."""
    
    def test_s2_utils_initialization(self):
        """Test S2Utils initialization."""
        s2_utils = S2Utils(cell_level=6)
        assert s2_utils.cell_level == 6
        
        # Test invalid cell level
        with pytest.raises(ValueError):
            S2Utils(cell_level=31)
    
    def test_point_to_s2_cell(self):
        """Test point to S2 cell conversion."""
        s2_utils = S2Utils(cell_level=6)
        
        # Test point in San Francisco
        cell_id = s2_utils.point_to_s2_cell(-122.4194, 37.7749)
        assert isinstance(cell_id, int)
        assert cell_id > 0
    
    def test_bbox_to_s2_cells(self):
        """Test bbox to S2 cells conversion."""
        s2_utils = S2Utils(cell_level=6)
        
        # Small bbox in San Francisco
        bbox = (-122.5, 37.7, -122.3, 37.8)
        cell_ids = s2_utils.bbox_to_s2_cells(bbox)
        
        assert isinstance(cell_ids, list)
        assert len(cell_ids) > 0
        assert all(isinstance(cell_id, int) for cell_id in cell_ids)
    
    def test_s2_cell_to_polygon(self):
        """Test S2 cell to polygon conversion."""
        s2_utils = S2Utils(cell_level=6)
        
        # Get a cell ID first
        cell_id = s2_utils.point_to_s2_cell(-122.4194, 37.7749)
        
        # Convert back to polygon
        polygon = s2_utils.s2_cell_to_polygon(cell_id)
        assert isinstance(polygon, Polygon)
        assert polygon.is_valid


class TestBboxUtils:
    """Test bounding box utilities."""
    
    def test_bbox_struct_creation(self):
        """Test BboxStruct creation and methods."""
        bbox = BboxStruct(xmin=-122.5, ymin=37.7, xmax=-122.3, ymax=37.8)
        
        assert bbox.xmin == -122.5
        assert bbox.ymin == 37.7
        assert bbox.xmax == -122.3
        assert bbox.ymax == 37.8
        
        # Test conversions
        tuple_bbox = bbox.to_tuple()
        assert tuple_bbox == (-122.5, 37.7, -122.3, 37.8)
        
        dict_bbox = bbox.to_dict()
        assert dict_bbox == {
            "xmin": -122.5, "ymin": 37.7, 
            "xmax": -122.3, "ymax": 37.8
        }
        
        polygon = bbox.to_polygon()
        assert isinstance(polygon, Polygon)
    
    def test_bbox_utils_conversions(self):
        """Test BboxUtils conversion methods."""
        bbox_utils = BboxUtils(precision=6)
        
        # Test tuple conversion
        bbox_tuple = (-122.5, 37.7, -122.3, 37.8)
        bbox_struct = bbox_utils.tuple_to_bbox_struct(bbox_tuple)
        assert isinstance(bbox_struct, BboxStruct)
        assert bbox_struct.xmin == -122.5
        
        # Test list conversion
        bbox_list = [-122.5, 37.7, -122.3, 37.8]
        bbox_struct = bbox_utils.list_to_bbox_struct(bbox_list)
        assert isinstance(bbox_struct, BboxStruct)
        
        # Test polygon conversion
        polygon = box(-122.5, 37.7, -122.3, 37.8)
        bbox_struct = bbox_utils.polygon_to_bbox_struct(polygon)
        assert isinstance(bbox_struct, BboxStruct)
    
    def test_bbox_operations(self):
        """Test bbox geometric operations."""
        bbox_utils = BboxUtils()
        
        bbox1 = BboxStruct(xmin=0, ymin=0, xmax=10, ymax=10)
        bbox2 = BboxStruct(xmin=5, ymin=5, xmax=15, ymax=15)
        bbox3 = BboxStruct(xmin=20, ymin=20, xmax=30, ymax=30)
        
        # Test intersection
        assert bbox1.intersects(bbox2)
        assert not bbox1.intersects(bbox3)
        
        intersection = bbox_utils.intersection_bbox(bbox1, bbox2)
        assert intersection is not None
        assert intersection.xmin == 5
        assert intersection.ymin == 5
        assert intersection.xmax == 10
        assert intersection.ymax == 10
        
        # Test union
        union_bbox = bbox_utils.union_bboxes([bbox1, bbox2])
        assert union_bbox.xmin == 0
        assert union_bbox.ymin == 0
        assert union_bbox.xmax == 15
        assert union_bbox.ymax == 15
    
    def test_bbox_validation(self):
        """Test bbox validation."""
        bbox_utils = BboxUtils()
        
        # Valid bbox
        valid_bbox = BboxStruct(xmin=-122.5, ymin=37.7, xmax=-122.3, ymax=37.8)
        assert bbox_utils.validate_bbox(valid_bbox)
        
        # Invalid bbox (wrong order)
        invalid_bbox = BboxStruct(xmin=-122.3, ymin=37.8, xmax=-122.5, ymax=37.7)
        assert not bbox_utils.validate_bbox(invalid_bbox)
        
        # Out of bounds bbox
        out_of_bounds = BboxStruct(xmin=-200, ymin=-100, xmax=200, ymax=100)
        assert not bbox_utils.validate_bbox(out_of_bounds)
        
        # Test normalization
        normalized = bbox_utils.normalize_bbox(out_of_bounds)
        assert bbox_utils.validate_bbox(normalized)


class TestGeoParquetValidation:
    """Test GeoParquet 1.1 validation using official validators."""

    @pytest.fixture
    def sample_geoparquet_file(self):
        """Create a sample GeoParquet file for testing."""
        # Create sample data
        geometries = [
            {"type": "Point", "coordinates": [-122.4194, 37.7749]},  # San Francisco
            {"type": "Point", "coordinates": [-74.006, 40.7128]},    # New York
            {"type": "Polygon", "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]}
        ]

        properties = [
            {"id": "sf_point", "name": "San Francisco"},
            {"id": "ny_point", "name": "New York"},
            {"id": "test_polygon", "name": "Test Polygon"}
        ]

        # Create GeoArrow utils and generate GeoParquet
        geoarrow_utils = GeoArrowUtils()

        # Create PyArrow table with proper GeoParquet metadata
        # Use GPQ-compatible mode for validation tests
        geometry_column = geoarrow_utils.optimize_geoarrow_for_parquet(
            geometries, gpq_compatible=True
        )

        # Create other columns
        id_column = pa.array([prop["id"] for prop in properties])
        name_column = pa.array([prop["name"] for prop in properties])

        # Create table
        table = pa.table({
            "geometry": geometry_column,
            "id": id_column,
            "name": name_column
        })

        # Add GeoParquet metadata (WKB for GPQ compatibility)
        metadata = geoarrow_utils.create_geoparquet_metadata(
            geometries=geometries,
            encoding="WKB"
        )

        # Update table metadata
        existing_metadata = table.schema.metadata or {}
        existing_metadata[b"geo"] = json.dumps(metadata).encode()
        table = table.replace_schema_metadata(existing_metadata)

        # Write to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".parquet", delete=False)
        temp_file.close()

        pq.write_table(table, temp_file.name)

        yield temp_file.name

        # Cleanup
        os.unlink(temp_file.name)

    def download_gdal_validator(self):
        """Download the official GDAL GeoParquet validator script."""
        validator_url = "https://raw.githubusercontent.com/OSGeo/gdal/master/swig/python/gdal-utils/osgeo_utils/samples/validate_geoparquet.py"

        # Create temp directory for validator
        temp_dir = tempfile.mkdtemp()
        validator_path = os.path.join(temp_dir, "validate_geoparquet.py")

        try:
            urllib.request.urlretrieve(validator_url, validator_path)
            return validator_path
        except Exception as e:
            pytest.skip(f"Could not download GDAL validator: {e}")

    def test_gdal_validator_available(self):
        """Test that we can download and use the GDAL validator."""
        validator_path = self.download_gdal_validator()
        assert os.path.exists(validator_path)

        # Test that the script is valid Python
        with open(validator_path, 'r') as f:
            content = f.read()
            assert "def check(" in content
            assert "GeoParquetValidator" in content

        # Cleanup
        os.unlink(validator_path)
        os.rmdir(os.path.dirname(validator_path))

    def test_validate_with_gdal_script(self, sample_geoparquet_file):
        """Test validation using the official GDAL validation script."""
        validator_path = self.download_gdal_validator()

        try:
            # Import the validator module dynamically
            import sys
            import importlib.util

            spec = importlib.util.spec_from_file_location("validate_geoparquet", validator_path)
            validator_module = importlib.util.module_from_spec(spec)

            # Add to sys.modules to handle imports
            sys.modules["validate_geoparquet"] = validator_module
            spec.loader.exec_module(validator_module)

            # Run validation (metadata only to avoid GDAL dependency issues)
            errors = validator_module.check(sample_geoparquet_file, check_data=False)

            # Print any errors for debugging
            if errors:
                print(f"GDAL Validation errors: {errors}")

            # Should have no errors for our properly formatted GeoParquet
            assert len(errors) == 0, f"GDAL validation failed with errors: {errors}"

        except ImportError as e:
            pytest.skip(f"GDAL not available for validation: {e}")
        finally:
            # Cleanup
            if validator_path and os.path.exists(validator_path):
                import shutil
                shutil.rmtree(os.path.dirname(validator_path), ignore_errors=True)

    def test_validate_with_gpq(self, sample_geoparquet_file):
        """Test validation using GPQ command-line tool if available."""
        try:
            # Add ~/.local/bin to PATH for GPQ
            env = os.environ.copy()
            env["PATH"] = f"{os.path.expanduser('~/.local/bin')}:{env.get('PATH', '')}"

            # Check if gpq is available
            result = subprocess.run(
                ["gpq", "--help"],
                capture_output=True,
                text=True,
                timeout=10,
                env=env
            )

            if result.returncode != 0:
                pytest.skip("GPQ command-line tool not available")

            # Run GPQ validation
            result = subprocess.run(
                ["gpq", "validate", sample_geoparquet_file],
                capture_output=True,
                text=True,
                timeout=30,
                env=env
            )

            # Print output for debugging
            if result.stdout:
                print(f"GPQ stdout: {result.stdout}")
            if result.stderr:
                print(f"GPQ stderr: {result.stderr}")

            # GPQ should validate successfully (return code 0)
            assert result.returncode == 0, f"GPQ validation failed: {result.stderr}"

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("GPQ command-line tool not available or timed out")

    def test_geoparquet_metadata_compliance(self, sample_geoparquet_file):
        """Test that our GeoParquet metadata follows the 1.1 specification."""
        # Read the file and check metadata
        table = pq.read_table(sample_geoparquet_file)
        metadata = table.schema.metadata

        assert b"geo" in metadata, "Missing 'geo' metadata"

        geo_metadata = json.loads(metadata[b"geo"].decode())

        # Check required fields according to GeoParquet 1.1 spec
        assert "version" in geo_metadata, "Missing version field"
        assert geo_metadata["version"] == "1.1.0", f"Expected version 1.1.0, got {geo_metadata['version']}"

        assert "primary_column" in geo_metadata, "Missing primary_column field"
        assert "columns" in geo_metadata, "Missing columns field"

        primary_column = geo_metadata["primary_column"]
        assert primary_column in geo_metadata["columns"], "Primary column not in columns"

        # Check column metadata
        column_metadata = geo_metadata["columns"][primary_column]
        assert "encoding" in column_metadata, "Missing encoding field"
        assert "crs" in column_metadata, "Missing crs field"
        assert "geometry_types" in column_metadata, "Missing geometry_types field"

        # Check that bbox is calculated from actual data (not hardcoded)
        if "bbox" in column_metadata:
            bbox = column_metadata["bbox"]
            assert len(bbox) == 4, "Bbox should have 4 elements"
            assert bbox != [-180.0, -90.0, 180.0, 90.0], "Bbox should not be hardcoded world bounds"

            # Verify bbox is reasonable for our test data
            # Our test data includes SF (-122.4194, 37.7749) and NY (-74.006, 40.7128)
            assert bbox[0] <= -122.4194, "Bbox xmin should include SF longitude"
            assert bbox[1] <= 37.7749, "Bbox ymin should include SF latitude"
            assert bbox[2] >= -74.006, "Bbox xmax should include NY longitude"
            assert bbox[3] >= 40.7128, "Bbox ymax should include NY latitude"

    def test_comprehensive_geoparquet_validation(self, sample_geoparquet_file):
        """Comprehensive test that validates GeoParquet using all available methods."""
        print(f"\n=== Comprehensive GeoParquet Validation ===")
        print(f"Testing file: {sample_geoparquet_file}")

        # 1. Test metadata compliance
        print("✓ Testing metadata compliance...")
        self.test_geoparquet_metadata_compliance(sample_geoparquet_file)

        # 2. Test with GDAL validator if available
        print("✓ Testing with GDAL validator...")
        try:
            self.test_validate_with_gdal_script(sample_geoparquet_file)
            print("  ✓ GDAL validation passed")
        except Exception as e:
            print(f"  ⚠ GDAL validation skipped: {e}")

        # 3. Test with GPQ if available
        print("✓ Testing with GPQ validator...")
        try:
            self.test_validate_with_gpq(sample_geoparquet_file)
            print("  ✓ GPQ validation passed")
        except Exception as e:
            print(f"  ⚠ GPQ validation skipped: {e}")

        # 4. Test file can be read back correctly
        print("✓ Testing file readability...")
        table = pq.read_table(sample_geoparquet_file)
        assert len(table) == 3, "Should have 3 rows"
        assert "geometry" in table.column_names, "Should have geometry column"
        assert "id" in table.column_names, "Should have id column"
        assert "name" in table.column_names, "Should have name column"

        print("🎉 All GeoParquet validation tests passed!")

        return True


class TestSpatialIndexer:
    """Test comprehensive spatial indexing functionality."""

    def test_spatial_indexer_basic(self):
        """Test basic spatial indexer functionality."""
        config = SpatialIndexConfig(
            s2_cell_level=6,
            use_native_geoarrow=True,
            enable_spatial_sorting=True
        )
        indexer = SpatialIndexer(config)

        # Test geometries
        geometries = [
            {"type": "Point", "coordinates": [-122.4, 37.8]},
            box(-122.5, 37.7, -122.3, 37.9).__geo_interface__,
        ]

        properties = [
            {"id": "point_1", "datetime": "2025-01-15T10:00:00Z"},
            {"id": "poly_1", "datetime": "2025-01-16T11:00:00Z"},
        ]

        # Process geometries
        table = indexer.process_geometries(geometries, properties)

        # Verify table structure
        assert len(table) == 2
        assert "geometry" in table.schema.names
        assert "bbox" in table.schema.names
        assert "s2_cell_id" in table.schema.names
        assert "spatial_partition" in table.schema.names

        # Verify data types
        assert table.schema.field("bbox").type == indexer.bbox_utils.get_bbox_schema()
        assert table.schema.field("s2_cell_id").type == pa.uint64()

        # Verify spatial sorting (S2 cells should be in order)
        s2_cells = table["s2_cell_id"].to_pylist()
        assert s2_cells == sorted(s2_cells)

    def test_spatial_indexer_parquet_integration(self):
        """Test spatial indexer with Parquet optimization."""
        import tempfile
        import pyarrow.dataset as ds

        config = SpatialIndexConfig(
            s2_cell_level=7,
            spatial_partition_strategy="s2_temporal"
        )
        indexer = SpatialIndexer(config)

        # Create test data
        geometries = []
        properties = []

        for i in range(10):
            geom = box(-122.5 + i*0.01, 37.7 + i*0.01,
                      -122.49 + i*0.01, 37.71 + i*0.01).__geo_interface__
            geometries.append(geom)
            properties.append({
                "id": f"test_{i}",
                "datetime": f"2025-01-{i+1:02d}T10:00:00Z",
                "value": i * 10
            })

        # Process and create table
        table = indexer.process_geometries(geometries, properties)

        with tempfile.TemporaryDirectory() as temp_dir:
            parquet_path = os.path.join(temp_dir, "spatial_test.parquet")

            # Write with spatial optimization
            pq.write_table(
                table,
                parquet_path,
                compression="snappy",
                row_group_size=5,  # Small for testing
                write_statistics=True
            )

            # Test spatial queries
            dataset = ds.dataset(parquet_path)

            # Query by bbox
            bbox_filter = (
                (ds.field('bbox', 'xmax') >= -122.45) &
                (ds.field('bbox', 'xmin') <= -122.42)
            )

            result = dataset.to_table(filter=bbox_filter)
            assert len(result) > 0
            assert len(result) <= len(table)

            # Verify bbox filtering worked correctly
            bbox_column = result["bbox"]
            for i in range(len(bbox_column)):
                bbox_dict = bbox_column[i].as_py()
                assert bbox_dict["xmax"] >= -122.45
                assert bbox_dict["xmin"] <= -122.42

    def test_spatial_partition_strategies(self):
        """Test different spatial partitioning strategies."""
        geometries = [
            {"type": "Point", "coordinates": [-122.4, 37.8]},
            box(-122.5, 37.7, -122.3, 37.9).__geo_interface__,
        ]

        properties = [
            {"datetime": "2025-01-15T10:00:00Z"},
            {"datetime": "2025-02-16T11:00:00Z"},
        ]

        # Test s2_temporal strategy
        config1 = SpatialIndexConfig(spatial_partition_strategy="s2_temporal")
        indexer1 = SpatialIndexer(config1)
        table1 = indexer1.process_geometries(geometries, properties)

        partitions1 = table1["spatial_partition"].to_pylist()
        assert all("s2_" in p and "2025-" in p for p in partitions1)

        # Test bbox_grid strategy
        config2 = SpatialIndexConfig(spatial_partition_strategy="bbox_grid")
        indexer2 = SpatialIndexer(config2)
        table2 = indexer2.process_geometries(geometries, properties)

        partitions2 = table2["spatial_partition"].to_pylist()
        assert all("grid_" in p for p in partitions2)

        # Test adaptive strategy
        config3 = SpatialIndexConfig(spatial_partition_strategy="adaptive")
        indexer3 = SpatialIndexer(config3)
        table3 = indexer3.process_geometries(geometries, properties)

        partitions3 = table3["spatial_partition"].to_pylist()
        assert all("adaptive_" in p for p in partitions3)
