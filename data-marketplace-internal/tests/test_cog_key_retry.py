# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# license: Terrafloww Labs Proprietary

import pytest
from typing import List, Dict, Any

from data_marketplace.ingestion.stac_processor import StacProcessor
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor

# Reuse simple dummies similar to test_stac_processor

class DummyAsset:
    def __init__(self, href: str, media_type: str = "image/tiff", roles=None, title=None, description=None, extra_fields=None):
        self.href = href
        self.media_type = media_type
        self.roles = roles
        self.title = title
        self.description = description
        self.extra_fields = extra_fields or {}

class DummyItem:
    def __init__(self, item_id: str, collection: str = "COLL", properties=None, assets=None, geometry=None, bbox=None):
        self.id = item_id
        self.collection_id = collection
        self.properties = properties or {"datetime": "2025-01-01T00:00:00Z"}
        self.assets = assets or {}
        self.geometry = geometry
        self.bbox = bbox

    def to_dict(self):
        return {
            "id": self.id,
            "collection": self.collection_id,
            "properties": self.properties,
            "assets": {k: {"href": v.href, "type": v.media_type, **v.extra_fields} for k, v in self.assets.items()},
            "geometry": self.geometry,
            "bbox": self.bbox,
        }

@pytest.mark.asyncio
@pytest.mark.unit
async def test_processor_skips_when_all_keys_present(monkeypatch):
    schema = UnifiedStacSchema()
    cog_proc = StacCogProcessor()

    # existing_key_checker returns both keys, so nothing should be processed
    processor = StacProcessor(schema, cog_proc, existing_key_checker=lambda sid, coll: {"B04", "B03"})

    item = DummyItem(
        "scene-1",
        assets={
            "B04": DummyAsset("https://example.com/B04.tif", media_type="image/tiff; profile=cloud-optimized"),
            "B03": DummyAsset("https://example.com/B03.tif", media_type="image/tiff; profile=cloud-optimized"),
        },
    )

    # If parse is called, fail test; it should not be called
    async def fail_parse(*args, **kwargs):
        raise AssertionError("parse_cog_headers_for_item should not be invoked when all keys exist")

    monkeypatch.setattr(StacCogProcessor, "parse_cog_headers_for_item", fail_parse)

    recs = await processor.create_unified_records_for_item(item, max_concurrent_requests=1)
    assert recs == []

@pytest.mark.asyncio
@pytest.mark.unit
async def test_processor_only_parses_missing_keys(monkeypatch):
    schema = UnifiedStacSchema()
    cog_proc = StacCogProcessor()

    # existing_key_checker returns only B04, so B03 is missing
    processor = StacProcessor(schema, cog_proc, existing_key_checker=lambda sid, coll: {"B04"})

    item = DummyItem(
        "scene-1",
        assets={
            "B04": DummyAsset("https://example.com/B04.tif", media_type="image/tiff; profile=cloud-optimized"),
            "B03": DummyAsset("https://example.com/B03.tif", media_type="image/tiff; profile=cloud-optimized"),
        },
    )

    # Capture only_keys passed into parser and return only one record for B03
    captured: Dict[str, Any] = {}

    async def fake_parse(self, stac_item, max_concurrent_requests, parser=None, only_keys=None):
        captured["only_keys"] = only_keys
        # Return a record only for the missing key
        return [
            {
                "asset_key": "B03",
                "asset_href": "https://example.com/B03.tif",
                "cog_width": 128,
                "cog_height": 128,
                "cog_tile_width": 128,
                "cog_tile_height": 128,
                "cog_dtype": "uint16",
            }
        ]

    monkeypatch.setattr(StacCogProcessor, "parse_cog_headers_for_item", fake_parse)

    recs = await processor.create_unified_records_for_item(item, max_concurrent_requests=1)
    assert len(recs) == 1
    assert recs[0]["cog_key"] == "B03"
    assert captured["only_keys"] == {"B03"}

@pytest.mark.asyncio
@pytest.mark.unit
async def test_processor_legacy_signature(monkeypatch):
    schema = UnifiedStacSchema()
    cog_proc = StacCogProcessor()
    processor = StacProcessor(schema, cog_proc, existing_key_checker=lambda sid, coll: set())

    item = DummyItem(
        "scene-1",
        assets={
            "B04": DummyAsset("https://example.com/B04.tif", media_type="image/tiff; profile=cloud-optimized"),
        },
    )

    # Legacy signature without only_keys should still work via fallback
    async def legacy_parse(self, stac_item, max_concurrent_requests):
        return [
            {
                "asset_key": "B04",
                "asset_href": "https://example.com/B04.tif",
                "cog_width": 64,
                "cog_height": 64,
                "cog_tile_width": 64,
                "cog_tile_height": 64,
                "cog_dtype": "uint16",
            }
        ]

    monkeypatch.setattr(StacCogProcessor, "parse_cog_headers_for_item", legacy_parse)

    recs = await processor.create_unified_records_for_item(item, max_concurrent_requests=1)
    assert len(recs) == 1
    assert recs[0]["cog_key"] == "B04"

