# SPDX-FileCopyrightText: Terrafloww Labs, 2025

import asyncio
from typing import List

import pytest

from data_marketplace.ingestion.stac_processor import StacProcessor
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor


class DummyAsset:
    def __init__(
        self,
        href: str,
        media_type: str = "image/tiff",
        roles=None,
        title=None,
        description=None,
        extra_fields=None,
    ):
        self.href = href
        self.media_type = media_type
        self.roles = roles
        self.title = title
        self.description = description
        self.extra_fields = extra_fields or {}


class DummyItem:
    def __init__(
        self,
        item_id: str,
        collection: str = "COLL",
        properties=None,
        assets=None,
        geometry=None,
        bbox=None,
    ):
        self.id = item_id
        self.collection = collection
        self.properties = properties or {"datetime": "2025-01-01T00:00:00Z"}
        self.assets = assets or {}
        self.geometry = geometry
        self.bbox = bbox

    def to_dict(self):
        return {
            "id": self.id,
            "collection": self.collection,
            "properties": self.properties,
            "assets": {
                k: {
                    "href": v.href,
                    "type": v.media_type,
                    "roles": v.roles,
                    "title": v.title,
                    "description": v.description,
                    **v.extra_fields,
                }
                for k, v in self.assets.items()
            },
            "geometry": self.geometry,
            "bbox": self.bbox,
        }


@pytest.mark.asyncio
@pytest.mark.unit
async def test_process_stac_batch_yields_records(monkeypatch):
    # Arrange
    schema = UnifiedStacSchema()
    cog_proc = StacCogProcessor()
    processor = StacProcessor(schema, cog_proc)

    # Build a dummy item with one COG asset; we'll mock COG parsing
    item = DummyItem(
        "scene-1",
        assets={
            "B04": DummyAsset(
                href="https://example.com/cog.tif",
                media_type="image/tiff; profile=cloud-optimized",
                roles=["data"],
                title="Red",
                extra_fields={"raster:bands": [{"scale": 0.0001, "offset": 0.0}]},
            )
        },
        geometry=None,
    )

    # Mock StacCogProcessor.parse_cog_headers_for_item to return a simple COG dict
    async def fake_parse_cog_headers_for_item(
        stac_item, max_concurrent_requests
    ) -> List[dict]:
        return [
            {
                "asset_key": "B04",
                "asset_href": "https://example.com/cog.tif",
                "cog_width": 256,
                "cog_height": 256,
                "cog_tile_width": 128,
                "cog_tile_height": 128,
                "cog_dtype": "uint16",
                "cog_compression": "deflate",
                "cog_predictor": 2,
                "cog_crs": "EPSG:4326",
                "cog_transform": [10.0, 0.0, 0.0, 0.0, -10.0, 0.0],
                "cog_tile_offsets": [0, 100],
                "cog_tile_byte_counts": [100, 100],
                "cog_scale": 0.0001,
                "cog_offset": 0.0,
            }
        ]

    monkeypatch.setattr(
        StacCogProcessor,
        "parse_cog_headers_for_item",
        lambda self, stac_item, max_concurrent_requests: fake_parse_cog_headers_for_item(
            stac_item, max_concurrent_requests
        ),
    )

    # Act
    async def run():
        async for (
            batch_stats,
            unified_records,
        ) in processor.process_stac_items_in_batches(
            iter([item]),
            batch_size=1,
            max_concurrent_cog_requests=1,
            max_concurrent_stac_items=1,
        ):
            # Assert
            assert batch_stats["unified_records_created"] == 1
            assert len(unified_records) == 1
            rec = unified_records[0]
            assert rec["scene_id"] == "scene-1"
            assert rec["cog_key"] == "B04"
            assert rec["cog_width"] == 256

    await run()
