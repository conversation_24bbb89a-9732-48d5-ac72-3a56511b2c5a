#!/usr/bin/env python3
"""
Test script for smart ingestion with missing date detection.
"""

import asyncio
import logging
import tempfile
import shutil
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_missing_date_detection():
    """Test the missing date detection logic."""
    from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester
    
    # Create temporary directory for testing
    temp_dir = Path(tempfile.mkdtemp())
    table_path = str(temp_dir / "test_table")
    
    try:
        logger.info("Testing missing date detection...")
        
        # Initialize ingester
        ingester = DeltaStacIngester(
            unified_table_path=table_path,
            storage_options={}
        )
        
        # Test with a small date range
        result = await ingester.ingest_stac_collection(
            stac_api_url="https://earth-search.aws.element84.com/v1",
            collection_id="sentinel-2-l2a",
            datetime_range="2025-01-01/2025-01-03",  # Just 2 days for testing
            batch_size=10,
            max_concurrent_cog_requests=5,
            max_concurrent_stac_items=2
        )
        
        logger.info(f"Test result: {result}")
        
        # Test again to see if it detects existing data
        logger.info("Running second ingestion to test missing date detection...")
        result2 = await ingester.ingest_stac_collection(
            stac_api_url="https://earth-search.aws.element84.com/v1",
            collection_id="sentinel-2-l2a",
            datetime_range="2025-01-01/2025-01-03",
            batch_size=10,
            max_concurrent_cog_requests=5,
            max_concurrent_stac_items=2
        )
        
        logger.info(f"Second test result: {result2}")
        
        # Test with a larger range that includes the existing data
        logger.info("Testing with larger range that includes existing data...")
        result3 = await ingester.ingest_stac_collection(
            stac_api_url="https://earth-search.aws.element84.com/v1",
            collection_id="sentinel-2-l2a",
            datetime_range="2025-01-01/2025-01-05",  # Includes existing + new dates
            batch_size=10,
            max_concurrent_cog_requests=5,
            max_concurrent_stac_items=2
        )
        
        logger.info(f"Third test result: {result3}")
        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    finally:
        # Clean up
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info("Cleaned up test directory")

async def test_restart_manager():
    """Test the RestartManager directly."""
    from data_marketplace.ingestion.restart_manager import RestartManager
    import pystac_client
    
    logger.info("Testing RestartManager...")
    
    # Create temporary directory for testing
    temp_dir = Path(tempfile.mkdtemp())
    table_path = str(temp_dir / "test_table")
    
    try:
        restart_manager = RestartManager(table_path, {})
        catalog = pystac_client.Client.open("https://earth-search.aws.element84.com/v1")
        
        # Test missing date detection on empty table
        missing_dates = restart_manager.find_missing_dates(
            catalog, 
            "sentinel-2-l2a", 
            "2025-01-01/2025-01-03"
        )
        
        logger.info(f"Missing dates (empty table): {missing_dates}")
        
        # Test STAC count for a single date
        stac_count = restart_manager.get_stac_count_for_date(
            catalog, 
            "sentinel-2-l2a", 
            "2025-01-01"
        )
        
        logger.info(f"STAC count for 2025-01-01: {stac_count}")
        
    except Exception as e:
        logger.error(f"RestartManager test failed: {e}", exc_info=True)
    finally:
        # Clean up
        if temp_dir.exists():
            shutil.rmtree(temp_dir)

async def main():
    """Run all tests."""
    logger.info("Starting smart ingestion tests...")
    
    # Test 1: RestartManager
    await test_restart_manager()
    
    # Test 2: Full ingestion with missing date detection
    await test_missing_date_detection()
    
    logger.info("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
