# SPDX-FileCopyrightText: Terrafloww Labs, 2025

import shutil
from pathlib import Path
from datetime import datetime

import pytest

from data_marketplace.ingestion.restart_manager import RestartManager


@pytest.mark.unit
def test_find_missing_dates_empty_table(monkeypatch, tmp_path: Path):
    # Arrange: empty local table path
    table_path = tmp_path / "unified_table"
    table_path.mkdir(parents=True, exist_ok=True)

    # Mock catalog and search.matched()
    class DummySearch:
        def __init__(self, count):
            self._count = count

        def matched(self):
            return self._count

    class DummyCatalog:
        def __init__(self, counts_by_range):
            self.counts_by_range = counts_by_range

        def search(self, **kwargs):
            dt = kwargs["datetime"]
            return DummySearch(self.counts_by_range.get(dt, 0))

    # For three days, provide STAC counts for each single-day window
    day1 = "2025-01-01T00:00:00/2025-01-02T00:00:00"
    day2 = "2025-01-02T00:00:00/2025-01-03T00:00:00"
    day3 = "2025-01-03T00:00:00/2025-01-04T00:00:00"
    catalog = DummyCatalog({day1: 2, day2: 0, day3: 1})

    rm = RestartManager(str(table_path))

    # Monkeypatch DeltaTable usage inside RestartManager.get_delta_count_for_date to avoid IO
    import data_marketplace.ingestion.restart_manager as rm_mod

    def fake_get_delta_count_for_date(date_str: str) -> int:
        return 0  # Empty table implies 0 for each date

    monkeypatch.setattr(
        rm_mod.RestartManager,
        "get_delta_count_for_date",
        lambda self, d: fake_get_delta_count_for_date(d),
    )

    # Act
    missing = rm.find_missing_dates(catalog, "sentinel-2", "2025-01-01/2025-01-03")

    # Assert: day1 and day3 have STAC>Delta -> missing; day2 STAC=0 => no missing
    assert missing == ["2025-01-01", "2025-01-03"]


@pytest.mark.unit
def test_find_missing_dates_counts_match(monkeypatch, tmp_path: Path):
    table_path = tmp_path / "unified_table"
    table_path.mkdir(parents=True, exist_ok=True)

    class DummySearch:
        def __init__(self, count):
            self._count = count

        def matched(self):
            return self._count

    class DummyCatalog:
        def __init__(self, counts_by_range):
            self.counts_by_range = counts_by_range

        def search(self, **kwargs):
            dt = kwargs["datetime"]
            return DummySearch(self.counts_by_range.get(dt, 0))

    # Provide STAC counts for 2 days, Delta mocked to same counts
    day1 = "2025-02-01T00:00:00/2025-02-02T00:00:00"
    day2 = "2025-02-02T00:00:00/2025-02-03T00:00:00"
    catalog = DummyCatalog({day1: 3, day2: 5})

    rm = RestartManager(str(table_path))

    import data_marketplace.ingestion.restart_manager as rm_mod

    # Mock Delta counts to match STAC
    def fake_get_delta_count_for_date(date_str: str) -> int:
        if date_str == "2025-02-01":
            return 3
        if date_str == "2025-02-02":
            return 5
        return 0

    monkeypatch.setattr(
        rm_mod.RestartManager,
        "get_delta_count_for_date",
        lambda self, d: fake_get_delta_count_for_date(d),
    )

    missing = rm.find_missing_dates(catalog, "sentinel-2", "2025-02-01/2025-02-02")
    assert missing == []
