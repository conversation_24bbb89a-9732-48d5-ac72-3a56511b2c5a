# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# license: Terrafloww Labs Proprietary

import pytest

from data_marketplace.ingestion.stac_processor import StacProcessor
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor

class Asset:
    def __init__(self, href: str, media_type: str):
        self.href = href
        self.media_type = media_type
        self.roles = None
        self.title = None
        self.description = None
        self.extra_fields = {}

class Item:
    def __init__(self):
        self.id = "scene-A"
        self.collection_id = "COLL"
        self.properties = {"datetime": "2025-01-01T00:00:00Z"}
        self.assets = {
            "B04": Asset("https://example.com/B04.tif", "image/tiff; profile=cloud-optimized"),
            "QA": Asset("https://example.com/QA.jp2", "image/jp2"),
        }

    def to_dict(self):
        return {
            "id": self.id,
            "collection": self.collection_id,
            "properties": self.properties,
            "assets": {
                k: {"href": v.href, "type": v.media_type}
                for k, v in self.assets.items()
            },
            "geometry": None,
            "bbox": None,
        }

@pytest.mark.asyncio
@pytest.mark.unit
async def test_non_cog_assets_not_counted_for_skip(monkeypatch):
    schema = UnifiedStacSchema()
    cog = StacCogProcessor()

    # Pretend that QA (a non-COG) exists in table; B04 is missing
    existing = {"QA"}
    proc = StacProcessor(schema, cog, existing_key_checker=lambda sid, coll: existing)

    # Intercept parse; expect only_keys to be {"B04"}
    captured = {}
    async def fake_parse(self, stac_item, max_concurrent_requests, parser=None, only_keys=None):
        captured["only_keys"] = only_keys
        return [
            {
                "asset_key": "B04",
                "asset_href": "https://example.com/B04.tif",
                "cog_width": 64,
                "cog_height": 64,
            }
        ]

    monkeypatch.setattr(StacCogProcessor, "parse_cog_headers_for_item", fake_parse)

    item = Item()
    recs = await proc.create_unified_records_for_item(item, max_concurrent_requests=1)
    assert len(recs) == 1
    assert recs[0]["cog_key"] == "B04"
    assert captured["only_keys"] == {"B04"}

