# Data Marketplace Internal

Production-ready unified STAC + COG ingestion system with Delta Lake storage and real COG header parsing.

## Overview

This system implements a **unified single-table approach** for STAC ingestion where each COG asset gets its own row with repeated scene metadata. This design optimizes for cross-collection spatial and temporal queries.

### Key Features

- **Unified Table Design**: One row per COG asset with complete scene + asset metadata
- **Real COG Header Parsing**: Async COG parser copied from rasteret with aiohttp
- **Delta Lake Storage**: ACID transactions, file consolidation, and time travel
- **Native GeoArrow**: Modern spatial data handling with 4-field bbox structs
- **S2 Spatial Indexing**: Global spatial coverage with anti-meridian safety
- **Cross-Collection Queries**: "Give me all data for this area with X resolution" → Single table scan

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   STAC APIs     │───▶│  Unified STAC    │───▶│   Delta Lake    │
│   (Earth Search │    │  + COG Ingester  │    │   Unified Table │
│    Element84)   │    │  - Real COG      │    │   - 1 row per   │
└─────────────────┘    │    parsing       │    │     COG asset   │
                       │  - aiohttp       │    │   - Repeated    │
                       │  - Batch proc.   │    │     scene data  │
                       └──────────────────┘    └─────────────────┘
                                                        │
                       ┌──────────────────┐            │
                       │  Query Engine    │◀───────────┘
                       │  - Cross-coll.   │
                       │    queries       │
                       │  - Spatial +     │
                       │    temporal      │
                       └──────────────────┘
```

## Quick Start

### Installation

```bash
cd data-marketplace-internal
uv sync
```

### Production Ingestion

```bash
# Ingest Sentinel-2 data for a specific area and time
uv run python scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "./data/unified_stac_table" \
    --datetime-range "2024-01-01/2024-01-31" \
    --bbox "2.0,46.0,3.0,47.0" \
    --batch-size 100 \
    --max-concurrent-cog 50
```

### Testing

```bash
# Test COG parser module
uv run python scripts/test_cog_parser.py

# Test unified ingestion with small sample
uv run python scripts/test_unified_ingestion.py
```

### Programmatic Usage

```python
from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester

# Initialize unified ingester
ingester = DeltaStacIngester(unified_table_path="./data/unified_table")

# Ingest STAC collection
results = await ingester.ingest_stac_collection(
    stac_api_url="https://earth-search.aws.element84.com/v1",
    collection_id="sentinel-2-l2a",
    max_items=1000,
    bbox=[2.0, 46.0, 3.0, 47.0],
    batch_size=100
)
```

## Components

### 1. COG Header Parsing (`src/data_marketplace/cog/`)
Real COG header parsing copied from rasteret with aiohttp:
- `AsyncCOGHeaderParser`: High-performance async COG parser
- `CogMetadata`: Type-safe COG metadata representation
- Connection pooling, caching, and retry logic

### 2. Unified Schema (`src/data_marketplace/ingestion/stac_schema.py`)
- `UnifiedStacSchema`: 45-field schema combining STAC + COG metadata
- Dictionary encoding for 12 high-cardinality columns
- Bloom filters for 6 columns (fast filtering)
- Native GeoArrow geometry support

### 3. Delta Lake Ingester (`src/data_marketplace/ingestion/delta_stac_ingester.py`)
- `DeltaStacIngester`: Production-ready unified ingestion
- Batch processing with concurrent COG header parsing
- Delta Lake optimization and file consolidation
- Comprehensive error handling and statistics

## Schema Details

### Unified Schema (45 fields)
```
Scene Metadata (repeated per COG asset):
├── scene_id, collection, datetime, platform, etc.
├── geometry (Native GeoArrow), bbox (4-field struct)
├── S2 spatial indexing: s2_cell_id, s2_cells, s2_level
└── Temporal: year, month, day

COG Asset Metadata (asset-specific):
├── cog_key, cog_href, cog_title, cog_roles
├── Dimensions: cog_width, cog_height, cog_tile_width, cog_tile_height
├── Technical: cog_dtype, cog_compression, cog_predictor, cog_crs
└── Arrays: cog_tile_offsets, cog_tile_byte_counts
```

### Optimization Features
- **Dictionary encoding**: 12 columns (collection, platform, cog_dtype, etc.)
- **Bloom filters**: 6 columns (scene_id, cog_key, cog_href, etc.)
- **Partitioning**: year/month for temporal queries
- **Delta Lake**: ACID transactions, file consolidation, time travel

## Query Examples

### Cross-Collection Spatial Query
```sql
SELECT scene_id, cog_key, cog_href, datetime
FROM unified_stac_table
WHERE bbox.xmin >= 2.0 AND bbox.xmax <= 3.0
  AND bbox.ymin >= 46.0 AND bbox.ymax <= 47.0
  AND year = 2024
  AND cog_dtype = 'uint16'
```

### Technical Filtering
```sql
SELECT collection, COUNT(*) as asset_count
FROM unified_stac_table
WHERE cog_width = 10980  -- 10m resolution
  AND cog_compression = 'deflate'
  AND year BETWEEN 2023 AND 2024
GROUP BY collection
```

## VM Requirements & Configuration

### For Million-Scale Ingestion

#### Recommended AWS Instance Types

**Option 1: c5n.24xlarge (Recommended)**
- **vCPUs**: 96 cores
- **Memory**: 192 GB RAM
- **Network**: 100 Gbps (critical for COG parsing)
- **Storage**: 3.8 TB NVMe SSD
- **Cost**: ~$4.60/hour
- **Performance**: 1M scenes in 2-3 days

**Option 2: c5n.18xlarge (Budget Option)**
- **vCPUs**: 72 cores
- **Memory**: 192 GB RAM
- **Network**: 75 Gbps
- **Storage**: 1.9 TB NVMe SSD
- **Cost**: ~$3.46/hour
- **Performance**: 1M scenes in 3-4 days

**Option 3: c5n.12xlarge (Minimum)**
- **vCPUs**: 48 cores
- **Memory**: 128 GB RAM
- **Network**: 50 Gbps
- **Storage**: 1.9 TB NVMe SSD
- **Cost**: ~$2.30/hour
- **Performance**: 1M scenes in 4-6 days

#### Optimized Configuration for Scale

```bash
# For c5n.24xlarge (96 cores)
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --batch-size 5000 \
    --max-concurrent-cog 500 \
    --max-concurrent-stac 30 \
    --output-path "s3://your-bucket/unified_stac_table"

# For c5n.18xlarge (72 cores)
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --batch-size 4000 \
    --max-concurrent-cog 400 \
    --max-concurrent-stac 25 \
    --output-path "s3://your-bucket/unified_stac_table"

# For c5n.12xlarge (48 cores)
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --batch-size 3000 \
    --max-concurrent-cog 300 \
    --max-concurrent-stac 20 \
    --output-path "s3://your-bucket/unified_stac_table"
```

### Performance Optimizations

#### Key Improvements Made
- **Parallel STAC Processing**: Process 20-30 STAC items simultaneously (not just COG headers)
- **Larger Batches**: 2000-5000 items per batch (vs 200 previously)
- **Higher COG Concurrency**: 200-500 concurrent requests (vs 50 previously)
- **Aligned Row Groups**: 128MB row groups match target file size
- **Better Defaults**: Optimized for production scale out of the box

#### Expected Performance
- **c5n.24xlarge**: 15,000-25,000 items/hour = 1M scenes in 40-67 hours
- **c5n.18xlarge**: 12,000-20,000 items/hour = 1M scenes in 50-83 hours
- **c5n.12xlarge**: 8,000-15,000 items/hour = 1M scenes in 67-125 hours

#### Network Proximity Critical
- **Run in same AWS region as S3 bucket** (us-west-2 for Earth Search)
- **100 Gbps networking essential** for COG header parsing performance
- **Avoid cross-region data transfer** costs and latency

### Monitoring & Troubleshooting

#### Progress Monitoring
```bash
# Monitor ingestion progress
tail -f /var/log/ingestion.log | grep "Processed.*items"

# Expected log patterns:
# INFO - Processed 2000 items, 32000 COG assets, Written 32000 unified records
# INFO - Processed batch 1/1 (16 headers) in 2.30s. Completed!
```

#### Performance Indicators
- **COG parsing time**: Should be 1-5 seconds per STAC item
- **Batch completion**: 2000-5000 items every 10-30 minutes
- **Memory usage**: Should stay under 80% of available RAM
- **Network utilization**: Should see high network I/O during COG parsing

#### Common Issues & Solutions

**Slow COG Parsing (>10s per item):**
- Reduce `--max-concurrent-cog` (network saturation)
- Check network connectivity to S3
- Verify running in correct AWS region

**Memory Issues:**
- Reduce `--batch-size`
- Reduce `--max-concurrent-stac`
- Monitor with `htop` or CloudWatch

**No Output Table:**
- Check that first batch completed (2000+ items processed)
- Verify write permissions to output path
- Check for error messages in logs

#### Resource Monitoring
```bash
# Monitor system resources
htop                    # CPU and memory usage
iotop                   # Disk I/O
nethogs                 # Network usage per process
df -h                   # Disk space
```

## Implementation Status

- [x] **Unified STAC Ingestion** - Complete with real COG parsing
- [x] **COG Header Parsing** - Async parser copied from rasteret
- [x] **Delta Lake Storage** - ACID transactions and optimization
- [x] **Spatial Indexing** - S2 cells and 4-field bbox structs
- [x] **Production Scripts** - Ready for high-speed ingestion
- [ ] **Query API Server** - DuckDB-based spatial queries (future)
- [ ] **Rasteret Integration** - API-based collection loading (future)

## License

Terrafloww Labs Proprietary
