<!-- SPDX-FileCopyrightText: Terrafloww Labs, 2025 -->
<!-- license: Terrafloww Labs Proprietary -->

## COG-key-level Retry: Minimal Changes Implemented

- RestartManager (src/data_marketplace/ingestion/restart_manager.py)
  - Added get_existing_cog_keys_for_scene(scene_id, collection=None) -> set[str]
  - Uses DuckDB delta_scan on the unified Delta table to return distinct cog_key for the given scene (optionally filtered by collection). Handles local/S3, credentials, and empty-table cases.

- StacCogProcessor (src/data_marketplace/cog/stac_cog_processor.py)
  - parse_cog_headers_for_item signature extended with only_keys: Optional[set]
  - Filters the asset list to parse only keys in only_keys when provided; otherwise unchanged.

- StacProcessor (src/data_marketplace/ingestion/stac_processor.py)
  - __init__ now accepts existing_key_checker: Optional callable(scene_id, collection) -> set[str].
  - create_unified_records_for_item computes missing keys per item:
    - Fetches existing keys via existing_key_checker
    - Computes missing = set(item.assets.keys()) - existing
    - If missing is empty, skips COG parsing for that item
    - Passes only_keys=missing to cog parsing
    - Backward compatible: falls back to legacy parse_cog_headers_for_item signature if monkeypatched tests don’t accept only_keys (TypeError guard)

- DeltaStacIngester (src/data_marketplace/ingestion/delta_stac_ingester.py)
  - Wires StacProcessor with existing_key_checker=lambda scene_id, collection: restart_manager.get_existing_cog_keys_for_scene(...)
  - No other behavior changes; MERGE dedup remains on ["scene_id", "cog_key"].

## Behavior Summary

- Date-level retry remains unchanged.
- Within each reprocessed date, for each STAC item/scene we now skip already-ingested COG keys and only parse+ingest missing ones.
- If the table is absent/empty or counts fail, the checker returns empty set, so ingestion proceeds as before.

## Next Plan (Short)

1) Tests
- Add unit tests for:
  - StacCogProcessor.only_keys filtering
  - StacProcessor path where existing_key_checker returns some/all keys
  - Back-compat: monkeypatched parse_cog_headers_for_item without only_keys still works
- Run pytest

2) Performance Hardening
- Avoid per-item DuckDB session churn: introduce a small LRU cache of scene_id -> existing_keys during a single-run process, or batch-check per-date using one DuckDB connection.
- Add a toggle (env/config) to enable/disable key-level retry if STAC source is small and overhead outweighs gains.

3) Docs
- Document the feature and any config/toggle in README.

4) Optional
- Expose a method to compute missing keys given a pystac.Item directly (helper that also filters to COG-likely keys).

