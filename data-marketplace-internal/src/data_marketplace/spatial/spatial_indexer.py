# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Comprehensive spatial indexing coordinator for optimal Parquet statistics and query performance."""

import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
import pyarrow as pa
import pyarrow.compute as pc
from shapely.geometry import shape, Polygon, Point
from .s2_utils import S2Utils
from .bbox_utils import BboxUtils, BboxStruct
from .geoarrow_utils import GeoArrowUtils

logger = logging.getLogger(__name__)


@dataclass
class SpatialIndexConfig:
    """Configuration for spatial indexing operations."""
    s2_cell_level: int = 6
    adaptive_s2_levels: bool = True
    max_s2_cells: int = 100
    bbox_precision: int = 6
    use_native_geoarrow: bool = True
    enable_spatial_sorting: bool = True
    spatial_partition_strategy: str = "s2_temporal"  # "s2_temporal", "bbox_grid", "adaptive"


class SpatialIndexer:
    """
    Comprehensive spatial indexing coordinator that integrates S2, bbox, and GeoArrow
    utilities for optimal Parquet statistics and query performance.
    """
    
    def __init__(self, config: Optional[SpatialIndexConfig] = None):
        """
        Initialize spatial indexer with configuration.
        
        Args:
            config: Spatial indexing configuration
        """
        self.config = config or SpatialIndexConfig()
        self.logger = logger
        
        # Initialize component utilities
        self.s2_utils = S2Utils(
            cell_level=self.config.s2_cell_level,
            adaptive_levels=self.config.adaptive_s2_levels
        )
        self.bbox_utils = BboxUtils(precision=self.config.bbox_precision)
        self.geoarrow_utils = GeoArrowUtils()
    
    def create_spatial_index_schema(self) -> pa.Schema:
        """
        Create PyArrow schema with spatial indexing fields.
        
        Returns:
            PyArrow schema with spatial index fields
        """
        fields = [
            # Core spatial fields
            pa.field("geometry", self.geoarrow_utils.get_geoarrow_schema()),
            pa.field("bbox", self.bbox_utils.get_bbox_schema()),
            
            # S2 spatial indexing (using strings for Delta Lake compatibility)
            pa.field("s2_cell_id", pa.string()),
            pa.field("s2_cell_level", pa.int8()),
            pa.field("s2_cells", pa.list_(pa.string())),  # Multiple cells for complex geometries
            
            # Spatial statistics for optimization
            pa.field("geometry_type", pa.string()),
            pa.field("geometry_complexity", pa.float32()),
            pa.field("spatial_area", pa.float64()),
            
            # Partition keys
            pa.field("spatial_partition", pa.string()),
        ]
        
        return pa.schema(fields)
    
    def process_geometries(
        self, 
        geometries: List[Dict[str, Any]],
        properties: Optional[List[Dict[str, Any]]] = None
    ) -> pa.Table:
        """
        Process geometries with comprehensive spatial indexing.
        
        Args:
            geometries: List of GeoJSON geometry dictionaries
            properties: Optional list of properties for each geometry
            
        Returns:
            PyArrow table with spatial indexing
        """
        if not geometries:
            return pa.table({}, schema=self.create_spatial_index_schema())
        
        self.logger.info(f"Processing {len(geometries)} geometries for spatial indexing")
        
        # Process geometries
        geometry_array = self._create_geometry_array(geometries)
        bbox_array = self._create_bbox_array(geometries)
        s2_data = self._create_s2_indexing(geometries)
        spatial_stats = self._calculate_spatial_statistics(geometries)
        partition_keys = self._generate_partition_keys(geometries, properties)
        
        # Combine all data
        table_data = {
            "geometry": geometry_array,
            "bbox": bbox_array,
            "s2_cell_id": pa.array(s2_data["primary_cells"], type=pa.uint64()),
            "s2_cell_level": pa.array(s2_data["cell_levels"], type=pa.int8()),
            "s2_cells": pa.array(s2_data["all_cells"], type=pa.list_(pa.uint64())),
            "geometry_type": pa.array(spatial_stats["types"], type=pa.string()),
            "geometry_complexity": pa.array(spatial_stats["complexity"], type=pa.float32()),
            "spatial_area": pa.array(spatial_stats["areas"], type=pa.float64()),
            "spatial_partition": pa.array(partition_keys, type=pa.string()),
        }
        
        # Add properties if provided
        if properties:
            for key in properties[0].keys() if properties else []:
                values = [prop.get(key) for prop in properties]
                table_data[key] = pa.array(values)
        
        table = pa.table(table_data)
        
        # Apply spatial sorting if enabled
        if self.config.enable_spatial_sorting:
            table = self._apply_spatial_sorting(table)
        
        self.logger.info(f"Created spatial index table with {len(table)} rows")
        return table
    
    def _create_geometry_array(self, geometries: List[Dict[str, Any]]) -> pa.Array:
        """Create optimized geometry array."""
        if self.config.use_native_geoarrow:
            return self.geoarrow_utils.optimize_geoarrow_for_parquet(
                geometries, 
                force_wkb=False, 
                gpq_compatible=False
            )
        else:
            return self.geoarrow_utils.geojson_to_wkb_array(geometries)
    
    def _create_bbox_array(self, geometries: List[Dict[str, Any]]) -> pa.Array:
        """Create 4-field bbox struct array."""
        bbox_structs = []
        
        for geom in geometries:
            if geom is None:
                bbox_structs.append(None)
            else:
                try:
                    shapely_geom = shape(geom)
                    bbox_struct = self.bbox_utils.polygon_to_bbox_struct(shapely_geom)
                    bbox_structs.append(bbox_struct)
                except Exception as e:
                    self.logger.warning(f"Failed to create bbox for geometry: {e}")
                    bbox_structs.append(None)
        
        return self.bbox_utils.create_bbox_array(bbox_structs)
    
    def _create_s2_indexing(self, geometries: List[Dict[str, Any]]) -> Dict[str, List]:
        """Create S2 spatial indexing data."""
        primary_cells = []
        cell_levels = []
        all_cells = []
        
        for geom in geometries:
            if geom is None:
                primary_cells.append(0)
                cell_levels.append(0)
                all_cells.append([])
            else:
                try:
                    shapely_geom = shape(geom)
                    
                    if isinstance(shapely_geom, Point):
                        # Single cell for points
                        cell_id = self.s2_utils.point_to_s2_cell(
                            shapely_geom.x, shapely_geom.y
                        )
                        primary_cells.append(cell_id)
                        cell_levels.append(self.s2_utils.cell_level)
                        all_cells.append([cell_id])
                    else:
                        # Multiple cells for polygons
                        cell_ids = self.s2_utils.polygon_to_s2_cells(
                            shapely_geom, max_cells=self.config.max_s2_cells
                        )
                        
                        if cell_ids:
                            primary_cells.append(cell_ids[0])  # First cell as primary
                            # Determine actual level used
                            try:
                                import s2sphere
                                cell = s2sphere.CellId(cell_ids[0])
                                cell_levels.append(cell.level())
                            except:
                                cell_levels.append(self.s2_utils.cell_level)
                            all_cells.append(cell_ids)
                        else:
                            primary_cells.append(0)
                            cell_levels.append(0)
                            all_cells.append([])
                            
                except Exception as e:
                    self.logger.warning(f"Failed to create S2 index for geometry: {e}")
                    primary_cells.append(0)
                    cell_levels.append(0)
                    all_cells.append([])
        
        return {
            "primary_cells": primary_cells,
            "cell_levels": cell_levels,
            "all_cells": all_cells
        }
    
    def _calculate_spatial_statistics(self, geometries: List[Dict[str, Any]]) -> Dict[str, List]:
        """Calculate spatial statistics for optimization."""
        types = []
        complexity = []
        areas = []
        
        for geom in geometries:
            if geom is None:
                types.append("null")
                complexity.append(0.0)
                areas.append(0.0)
            else:
                try:
                    shapely_geom = shape(geom)
                    
                    # Geometry type
                    types.append(shapely_geom.geom_type)
                    
                    # Complexity score (based on coordinate count)
                    if hasattr(shapely_geom, 'exterior'):
                        coord_count = len(shapely_geom.exterior.coords)
                    elif hasattr(shapely_geom, 'coords'):
                        coord_count = len(list(shapely_geom.coords))
                    else:
                        coord_count = 1
                    
                    complexity_score = min(1.0, coord_count / 100.0)  # Normalize to 0-1
                    complexity.append(complexity_score)
                    
                    # Area calculation
                    try:
                        area = shapely_geom.area
                        areas.append(area)
                    except:
                        areas.append(0.0)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to calculate stats for geometry: {e}")
                    types.append("unknown")
                    complexity.append(0.0)
                    areas.append(0.0)
        
        return {
            "types": types,
            "complexity": complexity,
            "areas": areas
        }
    
    def _generate_partition_keys(
        self, 
        geometries: List[Dict[str, Any]], 
        properties: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        """Generate spatial partition keys based on strategy."""
        partition_keys = []
        
        for i, geom in enumerate(geometries):
            if geom is None:
                partition_keys.append("unknown")
                continue
            
            try:
                if self.config.spatial_partition_strategy == "s2_temporal":
                    # Use S2 cell + temporal info if available
                    shapely_geom = shape(geom)
                    cell_id = self.s2_utils.point_to_s2_cell(
                        shapely_geom.centroid.x, shapely_geom.centroid.y, level=4
                    )
                    
                    # Add temporal component if available
                    if properties and i < len(properties):
                        datetime_str = properties[i].get("datetime", "")
                        if datetime_str:
                            year_month = datetime_str[:7]  # YYYY-MM
                            partition_keys.append(f"s2_{cell_id}_{year_month}")
                        else:
                            partition_keys.append(f"s2_{cell_id}")
                    else:
                        partition_keys.append(f"s2_{cell_id}")
                        
                elif self.config.spatial_partition_strategy == "bbox_grid":
                    # Use bbox grid partitioning
                    shapely_geom = shape(geom)
                    bounds = shapely_geom.bounds
                    
                    # Create 1-degree grid
                    grid_x = int(bounds[0])  # Floor of minx
                    grid_y = int(bounds[1])  # Floor of miny
                    partition_keys.append(f"grid_{grid_x}_{grid_y}")
                    
                else:  # adaptive
                    # Adaptive partitioning based on geometry complexity
                    shapely_geom = shape(geom)
                    area = shapely_geom.area
                    
                    if area > 1.0:  # Large area
                        level = 3
                    elif area > 0.1:  # Medium area
                        level = 4
                    else:  # Small area
                        level = 5
                    
                    cell_id = self.s2_utils.point_to_s2_cell(
                        shapely_geom.centroid.x, shapely_geom.centroid.y, level=level
                    )
                    partition_keys.append(f"adaptive_{level}_{cell_id}")
                    
            except Exception as e:
                self.logger.warning(f"Failed to generate partition key: {e}")
                partition_keys.append("unknown")
        
        return partition_keys
    
    def _apply_spatial_sorting(self, table: pa.Table) -> pa.Table:
        """Apply spatial sorting using S2 Hilbert curve order."""
        try:
            # Sort by primary S2 cell ID for spatial locality
            sort_indices = pc.sort_indices(table, sort_keys=[("s2_cell_id", "ascending")])
            sorted_table = pc.take(table, sort_indices)
            
            self.logger.debug("Applied spatial sorting using S2 Hilbert order")
            return sorted_table
            
        except Exception as e:
            self.logger.warning(f"Failed to apply spatial sorting: {e}")
            return table
