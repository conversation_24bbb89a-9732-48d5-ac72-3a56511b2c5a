# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Command-line interface for data marketplace operations."""

import click
import logging
from typing import Optional

from data_marketplace.config.settings import Settings


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.pass_context
def main(ctx, debug: bool):
    """Data Marketplace CLI - Utilities for STAC data processing."""
    # Set up logging
    log_level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Initialize settings
    settings = Settings()

    ctx.ensure_object(dict)
    ctx.obj['settings'] = settings


@main.command()
@click.pass_context
def version(ctx):
    """Show version information."""
    from data_marketplace import __version__
    click.echo(f"Data Marketplace v{__version__}")
    click.echo("Use scripts/ingest_stac_unified.py for production ingestion.")


@main.command()
@click.pass_context
def test_spatial(ctx):
    """Test spatial indexing utilities."""
    from data_marketplace.spatial.s2_utils import S2Utils
    from data_marketplace.spatial.bbox_utils import BboxUtils, BboxStruct
    
    click.echo("Testing spatial utilities...")
    
    # Test S2 utilities
    s2_utils = S2Utils(cell_level=6)
    
    # San Francisco point
    lon, lat = -122.4194, 37.7749
    cell_id = s2_utils.point_to_s2_cell(lon, lat)
    click.echo(f"Point ({lon}, {lat}) -> S2 cell: {cell_id}")
    
    # Convert back to polygon
    polygon = s2_utils.s2_cell_to_polygon(cell_id)
    click.echo(f"S2 cell bounds: {polygon.bounds}")
    
    # Test bbox utilities
    bbox_utils = BboxUtils()
    bbox = BboxStruct(xmin=-122.5, ymin=37.7, xmax=-122.3, ymax=37.8)
    
    click.echo(f"Bbox: {bbox.to_dict()}")
    click.echo(f"Area: {bbox.area():.6f} square degrees")
    
    # Get S2 cells for bbox
    cells = s2_utils.bbox_to_s2_cells(bbox.to_tuple())
    click.echo(f"Bbox covered by {len(cells)} S2 cells")
    
    click.echo("✓ Spatial utilities working correctly")


if __name__ == '__main__':
    main()
