# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Bulk processing module inspired by tiff-dumper's high-throughput patterns.

This module implements Phase 2 of the tiff-dumper integration strategy:
- Bulk processing with 50k-100k records per batch
- Advanced monitoring and statistics
- Memory pressure management
- Optimized Delta Lake writes

Maintains exact compatibility with existing schema while dramatically
improving throughput for large-scale ingestion scenarios.
"""

import asyncio
import logging
import time
import gc
import psutil
from typing import Dict, Any, List, Optional, AsyncIterator, Tuple
from dataclasses import dataclass, field

from .stac_processor import StacProcessor
from .stac_schema import UnifiedStacSchema
from ..cog.stac_cog_processor import StacCogProcessor

logger = logging.getLogger(__name__)


@dataclass
class BulkProcessingStats:
    """Advanced statistics for bulk processing."""
    items_processed: int = 0
    records_created: int = 0
    batches_written: int = 0
    errors: int = 0
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    memory_peak_mb: float = 0.0
    memory_current_mb: float = 0.0
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def items_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.items_processed / self.elapsed_time
        return 0.0
    
    @property
    def records_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.records_created / self.elapsed_time
        return 0.0
    
    @property
    def batches_per_hour(self) -> float:
        if self.elapsed_time > 0:
            return (self.batches_written / self.elapsed_time) * 3600
        return 0.0
    
    def update_memory(self):
        """Update memory statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()
        self.memory_current_mb = memory_info.rss / 1024 / 1024
        if self.memory_current_mb > self.memory_peak_mb:
            self.memory_peak_mb = self.memory_current_mb
    
    def update(self, items: int = 0, records: int = 0, batches: int = 0, errors: int = 0):
        """Update statistics."""
        self.items_processed += items
        self.records_created += records
        self.batches_written += batches
        self.errors += errors
        self.last_update = time.time()
        self.update_memory()


class BulkStacProcessor:
    """
    Bulk STAC processor using tiff-dumper's high-throughput patterns.
    
    This processor adopts tiff-dumper's bulk processing approach while
    maintaining exact compatibility with the existing unified schema.
    Key improvements:
    - 50k-100k records per batch (vs 2k-8k)
    - Advanced memory management
    - Real-time monitoring
    - Optimized for large-scale scenarios
    """
    
    def __init__(
        self,
        unified_schema: UnifiedStacSchema,
        cog_processor: StacCogProcessor,
        existing_key_checker: Optional[callable] = None,
        use_fast_parser: bool = True,
        bulk_batch_size: int = 50000,  # Much larger than traditional 2k-8k
        memory_pressure_threshold_mb: float = 2000.0,  # 2GB memory limit
        monitoring_interval: float = 10.0,  # Monitor every 10 seconds
        gc_interval: int = 5,  # Garbage collect every 5 batches
    ):
        """
        Initialize bulk STAC processor.
        
        Args:
            unified_schema: Schema for unified STAC + COG records
            cog_processor: COG header processor
            existing_key_checker: Function to check for existing COG keys
            use_fast_parser: Whether to use fast COG parser
            bulk_batch_size: Records per bulk batch (50k-100k recommended)
            memory_pressure_threshold_mb: Memory limit in MB
            monitoring_interval: Seconds between monitoring updates
            gc_interval: Batches between garbage collection
        """
        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.existing_key_checker = existing_key_checker
        self.use_fast_parser = use_fast_parser
        self.bulk_batch_size = bulk_batch_size
        self.memory_pressure_threshold_mb = memory_pressure_threshold_mb
        self.monitoring_interval = monitoring_interval
        self.gc_interval = gc_interval
        
        # Statistics tracking
        self.stats = BulkProcessingStats()
        
        # For compatibility with existing code
        self.stac_api_url = None
        
        logger.info(f"Initialized BulkStacProcessor with {bulk_batch_size} records per batch")
        logger.info(f"Memory pressure threshold: {memory_pressure_threshold_mb}MB")
    
    async def _check_memory_pressure(self) -> bool:
        """Check if memory pressure is too high."""
        self.stats.update_memory()
        if self.stats.memory_current_mb > self.memory_pressure_threshold_mb:
            logger.warning(f"Memory pressure high: {self.stats.memory_current_mb:.1f}MB > {self.memory_pressure_threshold_mb}MB")
            return True
        return False
    
    async def _force_garbage_collection(self):
        """Force garbage collection to free memory."""
        logger.debug("Forcing garbage collection...")
        gc.collect()
        await asyncio.sleep(0.1)  # Allow GC to complete
        self.stats.update_memory()
        logger.debug(f"Memory after GC: {self.stats.memory_current_mb:.1f}MB")
    
    async def _process_stac_items_bulk(
        self,
        stac_items: List[Any],
        max_concurrent_cog_requests: int = 500,  # Higher concurrency for bulk
    ) -> List[Dict[str, Any]]:
        """
        Process a bulk batch of STAC items.
        
        Args:
            stac_items: List of STAC items to process
            max_concurrent_cog_requests: Max concurrent COG requests
            
        Returns:
            List of unified records
        """
        unified_records = []
        
        logger.debug(f"Processing bulk batch of {len(stac_items)} STAC items")
        
        # Check memory pressure before processing
        if await self._check_memory_pressure():
            await self._force_garbage_collection()
        
        # Process items in smaller chunks to manage memory
        chunk_size = min(100, len(stac_items))  # Process 100 items at a time
        
        for i in range(0, len(stac_items), chunk_size):
            chunk = stac_items[i:i + chunk_size]
            
            # Process chunk
            chunk_records = []
            for stac_item in chunk:
                try:
                    # Check for existing keys if checker is available
                    only_keys = None
                    if self.existing_key_checker:
                        scene_id = getattr(stac_item, 'id', None)
                        if scene_id:
                            existing_keys = self.existing_key_checker(scene_id, None)
                            if existing_keys:
                                all_keys = set(stac_item.assets.keys()) if hasattr(stac_item, 'assets') else set()
                                missing_keys = all_keys - existing_keys
                                if not missing_keys:
                                    continue  # Skip if all keys exist
                                only_keys = missing_keys
                    
                    # Parse COG headers
                    if self.use_fast_parser:
                        cog_records = await self.cog_processor.parse_cog_headers_for_item_fast(
                            stac_item, max_concurrent_cog_requests, only_keys
                        )
                    else:
                        cog_records = await self.cog_processor.parse_cog_headers_for_item(
                            stac_item, max_concurrent_cog_requests, only_keys
                        )
                    
                    # Create unified records
                    if cog_records:
                        # Flatten STAC item once
                        item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
                        
                        # Inject federation metadata if available
                        if hasattr(self, 'stac_api_url') and self.stac_api_url:
                            item_dict['stac_api_url'] = self.stac_api_url
                        
                        base_scene = self.unified_schema.flatten_stac_item(item_dict)
                        
                        # Create unified records (one per COG asset)
                        for cog_record in cog_records:
                            cog = {
                                "cog_key": cog_record.get("asset_key"),
                                "cog_href": cog_record.get("asset_href"),
                                "cog_title": cog_record.get("asset_title"),
                                "cog_roles": cog_record.get("asset_roles", []),
                                "cog_width": cog_record.get("cog_width"),
                                "cog_height": cog_record.get("cog_height"),
                                "cog_tile_width": cog_record.get("cog_tile_width"),
                                "cog_tile_height": cog_record.get("cog_tile_height"),
                                "cog_dtype": cog_record.get("cog_dtype"),
                                "cog_compression": cog_record.get("cog_compression"),
                                "cog_predictor": cog_record.get("cog_predictor"),
                                "cog_crs": cog_record.get("cog_crs"),
                                "cog_transform": cog_record.get("cog_transform"),
                                "cog_tile_offsets": cog_record.get("cog_tile_offsets"),
                                "cog_tile_byte_counts": cog_record.get("cog_tile_byte_counts"),
                                "cog_scale": cog_record.get("cog_scale") or cog_record.get("scale"),
                                "cog_offset": cog_record.get("cog_offset") or cog_record.get("offset"),
                            }
                            unified_record = {**base_scene, **cog}
                            chunk_records.append(unified_record)
                            
                except Exception as e:
                    logger.error(f"Error processing STAC item {getattr(stac_item, 'id', 'unknown')}: {e}")
                    self.stats.update(errors=1)
            
            unified_records.extend(chunk_records)
            
            # Update statistics
            self.stats.update(items=len(chunk), records=len(chunk_records))
            
            # Check memory pressure after each chunk
            if await self._check_memory_pressure():
                await self._force_garbage_collection()
        
        logger.debug(f"Bulk batch complete: {len(stac_items)} items → {len(unified_records)} records")
        return unified_records

    async def _monitor_progress(self):
        """Monitor and log bulk processing progress."""
        while True:
            await asyncio.sleep(self.monitoring_interval)

            # Log comprehensive statistics
            logger.info(
                f"Bulk Processing Stats: "
                f"{self.stats.items_processed} items, "
                f"{self.stats.records_created} records, "
                f"{self.stats.batches_written} batches | "
                f"Speed: {self.stats.items_per_second:.1f} items/sec, "
                f"{self.stats.records_per_second:.1f} records/sec, "
                f"{self.stats.batches_per_hour:.1f} batches/hour | "
                f"Memory: {self.stats.memory_current_mb:.1f}MB "
                f"(peak: {self.stats.memory_peak_mb:.1f}MB) | "
                f"Errors: {self.stats.errors}"
            )

    async def process_stac_items_in_bulk_batches(
        self,
        stac_items: AsyncIterator,
        max_concurrent_cog_requests: int = 500,
        max_concurrent_stac_items: int = 50,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Process STAC items using bulk processing patterns.

        This method maintains compatibility with the existing interface while
        using tiff-dumper's bulk processing approach for maximum throughput.

        Args:
            stac_items: Iterator of STAC items to process
            max_concurrent_cog_requests: Max concurrent COG header requests
            max_concurrent_stac_items: Max concurrent STAC items (not used in bulk mode)

        Yields:
            Tuple of (batch_stats, unified_records) for each bulk batch
        """
        logger.info(f"Starting bulk STAC processing with {self.bulk_batch_size} records per batch")

        # Start monitoring task
        monitor_task = asyncio.create_task(self._monitor_progress())

        try:
            # Collect items into bulk batches
            bulk_batch = []
            batch_count = 0

            # Handle both async and regular iterators
            if hasattr(stac_items, '__aiter__'):
                # Async iterator
                async for stac_item in stac_items:
                    bulk_batch.append(stac_item)

                    # Process when bulk batch is full
                    if len(bulk_batch) >= self.bulk_batch_size:
                        unified_records = await self._process_stac_items_bulk(
                            bulk_batch, max_concurrent_cog_requests
                        )

                        # Create batch statistics
                        batch_stats = {
                            'stac_items_processed': len(bulk_batch),
                            'cog_assets_processed': len(unified_records),
                            'unified_records_written': len(unified_records),
                            'batch_number': batch_count + 1,
                            'is_bulk_batch': True,
                            'bulk_batch_size': self.bulk_batch_size,
                            'errors': [],  # Compatibility with existing code
                        }

                        yield batch_stats, unified_records

                        # Update statistics and cleanup
                        self.stats.update(batches=1)
                        batch_count += 1
                        bulk_batch = []

                        # Periodic garbage collection
                        if batch_count % self.gc_interval == 0:
                            await self._force_garbage_collection()
            else:
                # Regular iterator
                for stac_item in stac_items:
                    bulk_batch.append(stac_item)

                    # Process when bulk batch is full
                    if len(bulk_batch) >= self.bulk_batch_size:
                        unified_records = await self._process_stac_items_bulk(
                            bulk_batch, max_concurrent_cog_requests
                        )

                        # Create batch statistics
                        batch_stats = {
                            'stac_items_processed': len(bulk_batch),
                            'cog_assets_processed': len(unified_records),
                            'unified_records_written': len(unified_records),
                            'batch_number': batch_count + 1,
                            'is_bulk_batch': True,
                            'bulk_batch_size': self.bulk_batch_size,
                            'errors': [],  # Compatibility with existing code
                        }

                        yield batch_stats, unified_records

                        # Update statistics and cleanup
                        self.stats.update(batches=1)
                        batch_count += 1
                        bulk_batch = []

                        # Periodic garbage collection
                        if batch_count % self.gc_interval == 0:
                            await self._force_garbage_collection()

            # Process remaining items
            if bulk_batch:
                unified_records = await self._process_stac_items_bulk(
                    bulk_batch, max_concurrent_cog_requests
                )

                # Create batch statistics
                batch_stats = {
                    'stac_items_processed': len(bulk_batch),
                    'cog_assets_processed': len(unified_records),
                    'unified_records_written': len(unified_records),
                    'batch_number': batch_count + 1,
                    'is_bulk_batch': True,
                    'bulk_batch_size': len(bulk_batch),  # Final batch may be smaller
                    'errors': [],  # Compatibility with existing code
                }

                yield batch_stats, unified_records
                self.stats.update(batches=1)

        finally:
            # Stop monitoring
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass

        logger.info(f"Bulk processing completed: {self.stats.items_processed} items, {self.stats.records_created} records, {self.stats.batches_written} batches")
        logger.info(f"Final performance: {self.stats.items_per_second:.1f} items/sec, {self.stats.records_per_second:.1f} records/sec")
        logger.info(f"Memory usage: {self.stats.memory_current_mb:.1f}MB current, {self.stats.memory_peak_mb:.1f}MB peak")
