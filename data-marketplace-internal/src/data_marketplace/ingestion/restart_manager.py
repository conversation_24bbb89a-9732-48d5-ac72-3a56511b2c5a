# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Smart restart logic for STAC ingestion.

This module handles date-by-date comparison between STAC API and Delta Lake
to determine optimal restart points for interrupted ingestions.
"""

import logging
from typing import Optional, List, Dict

import pystac_client
from deltalake import DeltaTable
import os

logger = logging.getLogger(__name__)


class RestartManager:
    """Manages smart restart logic for STAC ingestion."""

    def __init__(self, table_path: str, storage_options: Optional[Dict[str, str]] = None):
        """
        Initialize restart manager.

        Args:
            table_path: Path to Delta Lake table
            storage_options: S3/cloud storage options
        """
        self.table_path = table_path
        self.storage_options = storage_options or {}

    def get_existing_cog_keys_for_scene(self, scene_id: str, collection: Optional[str] = None) -> set:
        """Return set of existing cog_key values already ingested for a given scene (and optional collection)."""
        try:
            # Delegate to range prefetch if available (fast path)
            if hasattr(self, "_prefetch_map") and isinstance(getattr(self, "_prefetch_map"), dict):
                return set(self._prefetch_map.get(scene_id, set()))

            # Fallback single-scene scan (legacy)
            if self.table_path.startswith("s3://"):
                try:
                    dt = DeltaTable(self.table_path, storage_options=self.storage_options)
                except Exception as e:
                    if "not found" in str(e).lower() or "does not exist" in str(e).lower():
                        return set()
                    else:
                        raise e
            else:
                if not os.path.exists(self.table_path):
                    return set()
                dt = DeltaTable(self.table_path, storage_options=self.storage_options)

            if len(dt.files()) == 0:
                return set()

            import duckdb
            conn = duckdb.connect()
            try:
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")

                if self.table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """)
                    else:
                        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")

                sid = scene_id.replace("'", "''")
                where = f"scene_id = '{sid}'"
                if collection:
                    coll = collection.replace("'", "''")
                    where += f" AND collection = '{coll}'"

                query = f"""
                    SELECT DISTINCT cog_key
                    FROM delta_scan('{self.table_path}')
                    WHERE {where}
                """
                rows = conn.execute(query).fetchall()
                return {r[0] for r in rows if r and r[0] is not None}
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception as e:
            logger.warning(f"Failed to fetch existing cog_keys for scene_id={scene_id}: {e}")
            return set()

    def prefetch_existing_cog_keys_for_range(self, datetime_range: str, bbox: Optional[List[float]] = None) -> Dict[str, set]:
        """Prefetch existing (scene_id -> set(cog_key)) once for the entire date range."""
        logger.info(f"Prefetching existing COG keys for range: {datetime_range}")
        try:
            # Reset map
            self._prefetch_map = {}

            # Check if table exists with files
            if self.table_path.startswith("s3://"):
                try:
                    dt = DeltaTable(self.table_path, storage_options=self.storage_options)
                except Exception as e:
                    if "not found" in str(e).lower() or "does not exist" in str(e).lower():
                        logger.info(f"Delta table not found: {self.table_path}")
                        return self._prefetch_map
                    else:
                        raise e
            else:
                if not os.path.exists(self.table_path):
                    logger.info(f"Delta table path does not exist: {self.table_path}")
                    return self._prefetch_map
                dt = DeltaTable(self.table_path, storage_options=self.storage_options)

            if len(dt.files()) == 0:
                logger.info(f"Delta table has no files: {self.table_path}")
                return self._prefetch_map

            import duckdb
            conn = duckdb.connect()
            try:
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")

                if self.table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """)
                    else:
                        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")

                start_date_str, end_date_str = datetime_range.split("/")
                # Handle timezone issues by expanding the date range
                # STAC queries for 2025-02-02 might return data with timestamps on 2025-02-03 due to timezones
                from datetime import datetime, timedelta
                start_dt = datetime.fromisoformat(start_date_str.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_date_str.replace('Z', '+00:00'))

                # Expand range by 1 day on each side to handle timezone shifts
                start_dt_expanded = start_dt - timedelta(days=1)
                end_dt_expanded = end_dt + timedelta(days=2)  # +2 to make it inclusive and handle timezone

                where = f"datetime >= '{start_dt_expanded.isoformat()}' AND datetime < '{end_dt_expanded.isoformat()}'"

                query = f"""
                    SELECT scene_id, cog_key
                    FROM delta_scan('{self.table_path}')
                    WHERE {where}
                """
                rows = conn.execute(query).fetchall()

                # Populate in-memory map
                for sid, key in rows:
                    if sid is None or key is None:
                        continue
                    s = self._prefetch_map.get(sid)
                    if s is None:
                        s = set()
                        self._prefetch_map[sid] = s
                    s.add(key)

                logger.info(f"Prefetched existing keys for range {datetime_range}: {len(self._prefetch_map)} scenes")
                return self._prefetch_map
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception as e:
            logger.warning(f"Failed prefetch for range {datetime_range}: {e}")
            self._prefetch_map = {}
            return self._prefetch_map

    def check_missing_cog_keys_for_range(
        self,
        catalog: pystac_client.Client,
        collection_id: str,
        datetime_range: str,
        bbox: Optional[List[float]] = None
    ) -> bool:
        """
        Check if there are any missing COG keys in the date range.

        Returns True if there are missing keys (ingestion needed), False if all keys exist.
        This is the correct pre-ingestion check that looks at actual COG key completeness.
        """
        try:
            logger.info(f"Checking COG key completeness for range: {datetime_range}")

            # First, prefetch existing keys for the range (this will be reused later)
            existing_keys_map = self.prefetch_existing_cog_keys_for_range(datetime_range, bbox)

            # If no existing keys found, we definitely need ingestion
            if not existing_keys_map:
                logger.info(f"No existing keys found for range {datetime_range} - ingestion needed")
                return True

            # Quick check: try to get just a few STAC items to see if STAC API is working
            search_params = {
                "collections": [collection_id],
                "datetime": datetime_range,
                "limit": 10  # Just check a few items for API health
            }
            if bbox:
                search_params["bbox"] = bbox

            search = catalog.search(**search_params)

            # Try to get just the first few items to test API responsiveness
            items_checked = 0
            for item in search.items():
                items_checked += 1
                scene_id = getattr(item, 'id', None)
                if not scene_id:
                    continue

                # Get COG assets from this item
                cog_keys = set()
                for key, asset in getattr(item, 'assets', {}).items():
                    # Check if this looks like a COG asset
                    media_type = getattr(asset, 'media_type', '')
                    if 'tiff' in media_type.lower() or 'cog' in media_type.lower():
                        cog_keys.add(key)

                # Check if all COG keys for this scene exist in Delta table
                existing_keys = existing_keys_map.get(scene_id, set())
                missing_keys = cog_keys - existing_keys

                if missing_keys:
                    logger.info(f"Found missing COG keys in sample - ingestion needed (checked {items_checked} items)")
                    return True

                # Only check a few items for the pre-check to avoid STAC API timeouts
                if items_checked >= 5:
                    break

            # If we checked some items and found no missing keys, assume all are complete
            # This is an optimization - the per-item filtering will catch any edge cases
            if items_checked > 0:
                logger.info(f"✅ Sample check shows all COG keys exist for range {datetime_range} - skipping ingestion")
                return False
            else:
                logger.info(f"No STAC items found for range {datetime_range} - skipping ingestion")
                return False

        except Exception as e:
            logger.warning(f"Error checking COG key completeness for range {datetime_range}: {e}")
            # If we can't determine due to STAC API issues, proceed with ingestion
            # The per-item filtering will handle the actual deduplication
            logger.info(f"Falling back to per-item filtering due to pre-check error")
            return True
    

    

    

    

