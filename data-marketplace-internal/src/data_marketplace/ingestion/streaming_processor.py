# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Streaming STAC processor using tiff-dumper's producer-consumer pattern.

This module implements Phase 1 of the tiff-dumper integration strategy:
- anyio memory streams for high-throughput processing
- Producer-consumer pattern for better memory management
- Real-time monitoring and statistics
- Task group-based concurrency control

Maintains exact compatibility with existing StacProcessor while improving
memory efficiency and providing monitoring capabilities.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, AsyncIterator, Tuple
from dataclasses import dataclass, field

import anyio
from anyio import create_memory_object_stream, create_task_group
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream

from .stac_processor import StacProcessor
from .stac_schema import UnifiedStacSchema
from ..cog.stac_cog_processor import StacCogProcessor

logger = logging.getLogger(__name__)


@dataclass
class CogWorkItem:
    """
    Individual COG processing work item - inspired by tiff-dumper architecture.

    This represents a single COG asset to be processed, allowing true
    COG-level parallelism in the streaming processor.
    """
    stac_item: Dict[str, Any]  # Full STAC item
    asset_key: str             # COG asset key (e.g., 'red', 'blue')
    asset_href: str            # COG URL
    asset_data: Dict[str, Any] # Asset metadata
    base_scene: Dict[str, Any] # Pre-flattened STAC metadata
    scene_id: str              # STAC item ID


@dataclass
class StreamingStats:
    """Real-time streaming statistics."""
    items_processed: int = 0
    records_created: int = 0
    errors: int = 0
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def items_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.items_processed / self.elapsed_time
        return 0.0
    
    @property
    def records_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.records_created / self.elapsed_time
        return 0.0
    
    def update(self, items: int = 0, records: int = 0, errors: int = 0):
        """Update statistics."""
        self.items_processed += items
        self.records_created += records
        self.errors += errors
        self.last_update = time.time()


class StreamingStacProcessor:
    """
    Streaming STAC processor using tiff-dumper's producer-consumer architecture.
    
    This processor maintains exact compatibility with the existing StacProcessor
    while adopting tiff-dumper's high-performance streaming patterns for better
    memory management and monitoring capabilities.
    """
    
    def __init__(
        self,
        unified_schema: UnifiedStacSchema,
        cog_processor: StacCogProcessor,
        existing_key_checker: Optional[callable] = None,
        use_fast_parser: bool = True,
        max_buffer_size: int = 10000,  # Smaller than tiff-dumper's 1M for now
        n_consumers: int = 50,  # Smaller than tiff-dumper's 1000 for now
        monitoring_interval: float = 5.0,  # Monitor every 5 seconds
    ):
        """
        Initialize streaming STAC processor.
        
        Args:
            unified_schema: Schema for unified STAC + COG records
            cog_processor: COG header processor
            existing_key_checker: Function to check for existing COG keys
            use_fast_parser: Whether to use fast COG parser
            max_buffer_size: Maximum items in memory streams
            n_consumers: Number of consumer tasks
            monitoring_interval: Seconds between monitoring updates
        """
        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.existing_key_checker = existing_key_checker
        self.use_fast_parser = use_fast_parser
        self.max_buffer_size = max_buffer_size
        self.n_consumers = n_consumers
        self.monitoring_interval = monitoring_interval
        
        # Statistics tracking
        self.stats = StreamingStats()
        
        # For compatibility with existing code
        self.stac_api_url = None
        self._existing_keys_cache = {}  # Cache for existing keys
        
        logger.info(f"Initialized StreamingStacProcessor with {n_consumers} consumers, buffer size {max_buffer_size}")
    
    async def _producer(
        self,
        send: MemoryObjectSendStream,
        stac_items,  # Can be async iterator or regular iterator
        max_concurrent_cog_requests: int,
        max_concurrent_stac_items: int,
    ):
        """
        Producer: Creates individual COG work items from STAC items (tiff-dumper style).

        This extracts all COG assets from each STAC item and creates individual
        CogWorkItems for true COG-level parallelism.

        Args:
            send: Stream to send CogWorkItems to
            stac_items: Iterator of STAC items to process (async or regular)
            max_concurrent_cog_requests: Max concurrent COG requests (unused here)
            max_concurrent_stac_items: Max concurrent STAC items (unused here)
        """
        logger.debug("Starting streaming producer (COG-level)")
        async with send:
            # Handle both async and regular iterators
            if hasattr(stac_items, '__aiter__'):
                # Async iterator
                async for stac_item in stac_items:
                    try:
                        await self._create_cog_work_items(stac_item, send)
                    except Exception as e:
                        logger.error(f"Producer error processing STAC item: {e}")
                        self.stats.update(errors=1)
            else:
                # Regular iterator
                for stac_item in stac_items:
                    try:
                        await self._create_cog_work_items(stac_item, send)
                    except Exception as e:
                        logger.error(f"Producer error processing STAC item: {e}")
                        self.stats.update(errors=1)
        logger.debug("Producer finished")

    async def _create_cog_work_items(self, stac_item, send: MemoryObjectSendStream):
        """Create individual CogWorkItems from a STAC item - reusing existing logic."""
        try:
            # Reuse the existing create_unified_records_for_item logic
            # This handles all the COG filtering, existing key checking, etc.
            unified_records = await self.create_unified_records_for_item(
                stac_item, max_concurrent_requests=50  # Use reasonable default
            )

            # Send each unified record as a work item
            for unified_record in unified_records:
                await send.send(unified_record)

        except Exception as e:
            logger.error(f"Error creating work items for STAC item: {e}")

    async def create_unified_records_for_item(
        self,
        stac_item,
        max_concurrent_requests: int,
    ) -> List[Dict[str, Any]]:
        """
        Create unified records for a STAC item - reusing traditional processor logic.

        This method is copied from the traditional StacProcessor to ensure
        identical behavior and compatibility.
        """
        unified_records = []

        try:
            # If we have a checker, compute which COG keys are already present and only process missing keys
            only_keys = None
            try:
                if self.existing_key_checker is not None:
                    scene_id = getattr(stac_item, 'id', None)
                    collection = getattr(stac_item, 'collection_id', None)
                    only_keys = None
                    if scene_id:
                        cache_key = (scene_id, collection)
                        if cache_key in self._existing_keys_cache:
                            existing = self._existing_keys_cache[cache_key]
                        else:
                            existing = self.existing_key_checker(scene_id, collection)
                            self._existing_keys_cache[cache_key] = set(existing or set())
                        if existing is not None:
                            # Determine candidate COG keys from item assets (filter to COG assets only)
                            candidate_keys = {
                                key for key, asset in getattr(stac_item, 'assets', {}).items()
                                if self.cog_processor.is_cog_asset(asset)
                            }
                            # Compute missing among COG candidates only
                            missing = candidate_keys - set(existing)
                            if missing:
                                only_keys = missing
                            else:
                                only_keys = set()  # nothing to do
            except Exception as e:
                logger.debug(f"existing_key_checker failed, proceeding without filter: {e}")
                only_keys = None

            # Parse COG headers for filtered assets (when only_keys is set)
            cog_records = []
            if only_keys is not None and len(only_keys) == 0:
                # Nothing to do for this item
                logger.debug(f"Skipping item {getattr(stac_item, 'id', 'unknown')} - all cog_keys already ingested")
            else:
                # Choose parser based on configuration
                if self.use_fast_parser and hasattr(self.cog_processor, 'parse_cog_headers_for_item_fast'):
                    # Use fast parser for better performance
                    cog_records = await self.cog_processor.parse_cog_headers_for_item_fast(
                        stac_item, max_concurrent_requests, only_keys=only_keys
                    )
                else:
                    # Backward-compatible call
                    try:
                        cog_records = await self.cog_processor.parse_cog_headers_for_item(
                            stac_item, max_concurrent_requests, only_keys=only_keys
                        )
                    except TypeError:
                        # Fallback to legacy signature
                        cog_records = await self.cog_processor.parse_cog_headers_for_item(
                            stac_item, max_concurrent_requests
                        )

            # Compute base scene record ONCE per item to avoid repeated heavy flattening
            try:
                item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item

                # Inject federation metadata if available
                if hasattr(self, 'stac_api_url') and self.stac_api_url:
                    item_dict['stac_api_url'] = self.stac_api_url

                base_scene = self.unified_schema.flatten_stac_item(item_dict)
            except Exception as e:
                logger.error(f"Error flattening STAC item {getattr(stac_item, 'id', 'unknown')}: {e}")
                base_scene = None

            # Create unified records (one per COG asset)
            for cog_record in cog_records:
                try:
                    if base_scene is None:
                        # Fallback to original create_unified_record path if flatten failed
                        unified_record = self.unified_schema.create_unified_record(
                            stac_item=stac_item, cog_metadata=cog_record
                        )
                    else:
                        # Map COG fields into unified schema names and merge with base_scene
                        cog = {
                            "cog_key": cog_record.get("asset_key"),
                            "cog_href": cog_record.get("asset_href"),
                            "cog_title": cog_record.get("asset_title"),
                            "cog_roles": cog_record.get("asset_roles", []),
                            "cog_width": cog_record.get("cog_width"),
                            "cog_height": cog_record.get("cog_height"),
                            "cog_tile_width": cog_record.get("cog_tile_width"),
                            "cog_tile_height": cog_record.get("cog_tile_height"),
                            "cog_dtype": cog_record.get("cog_dtype"),
                            "cog_compression": cog_record.get("cog_compression"),
                            "cog_predictor": cog_record.get("cog_predictor"),
                            "cog_crs": cog_record.get("cog_crs"),
                            "cog_transform": cog_record.get("cog_transform"),
                            "cog_tile_offsets": cog_record.get("cog_tile_offsets"),
                            "cog_tile_byte_counts": cog_record.get("cog_tile_byte_counts"),
                            "cog_scale": cog_record.get("cog_scale") or cog_record.get("scale"),
                            "cog_offset": cog_record.get("cog_offset") or cog_record.get("offset"),
                        }
                        unified_record = {**base_scene, **cog}
                    unified_records.append(unified_record)
                except Exception as e:
                    logger.error(
                        f"Error creating unified record for {getattr(stac_item, 'id', 'unknown')}, asset {cog_record.get('asset_key', 'unknown')}: {e}"
                    )
                    continue

            logger.debug(
                f"Created {len(unified_records)} unified records for STAC item {getattr(stac_item, 'id', 'unknown')}"
            )

        except Exception as e:
            logger.error(f"Error creating unified records for item {stac_item.id}: {e}")

        return unified_records
    
    async def _consumer(
        self,
        receive: MemoryObjectReceiveStream,
        send_output: MemoryObjectSendStream,
    ):
        """
        Consumer task: processes individual COG work items (tiff-dumper style).

        Args:
            receive: Stream to receive CogWorkItems from producer
            send_output: Stream to send processed unified records to output
        """
        logger.debug("Starting streaming consumer (COG-level)")
        async with send_output:
            async with receive:
                async for cog_work_item in receive:
                    try:
                        # Process individual COG
                        unified_record = await self._process_single_cog(cog_work_item)

                        if unified_record:
                            # Send result to output stream
                            await send_output.send(unified_record)

                            # Update statistics
                            self.stats.update(items=0, records=1)  # Count COGs, not STAC items

                    except Exception as e:
                        logger.error(f"Consumer error processing COG {cog_work_item.asset_key}: {e}")
                        self.stats.update(errors=1)
        logger.debug("Consumer finished")

    async def _process_single_cog(self, cog_work_item: CogWorkItem) -> Optional[Dict[str, Any]]:
        """
        Process a single COG work item to create a unified record.

        Args:
            cog_work_item: Individual COG processing work item

        Returns:
            Unified record or None if processing failed
        """
        try:
            # Parse COG headers for this specific asset
            if self.use_fast_parser:
                cog_records = await self.cog_processor.parse_cog_headers_for_asset_fast(
                    cog_work_item.asset_href,
                    cog_work_item.asset_key,
                    cog_work_item.asset_data
                )
            else:
                cog_records = await self.cog_processor.parse_cog_headers_for_asset(
                    cog_work_item.asset_href,
                    cog_work_item.asset_key,
                    cog_work_item.asset_data
                )

            if not cog_records:
                return None

            # Should only be one record for a single asset
            cog_record = cog_records[0] if cog_records else {}

            # Create unified record by combining base scene + COG data
            cog = {
                "cog_key": cog_record.get("asset_key"),
                "cog_href": cog_record.get("asset_href"),
                "cog_title": cog_record.get("asset_title"),
                "cog_roles": cog_record.get("asset_roles", []),
                "cog_width": cog_record.get("cog_width"),
                "cog_height": cog_record.get("cog_height"),
                "cog_tile_width": cog_record.get("cog_tile_width"),
                "cog_tile_height": cog_record.get("cog_tile_height"),
                "cog_dtype": cog_record.get("cog_dtype"),
                "cog_compression": cog_record.get("cog_compression"),
                "cog_predictor": cog_record.get("cog_predictor"),
                "cog_crs": cog_record.get("cog_crs"),
                "cog_transform": cog_record.get("cog_transform"),
                "cog_tile_offsets": cog_record.get("cog_tile_offsets"),
                "cog_tile_byte_counts": cog_record.get("cog_tile_byte_counts"),
                "cog_scale": cog_record.get("cog_scale") or cog_record.get("scale"),
                "cog_offset": cog_record.get("cog_offset") or cog_record.get("offset"),
            }

            # Combine base scene + COG data
            unified_record = {**cog_work_item.base_scene, **cog}
            return unified_record

        except Exception as e:
            logger.error(f"Error processing COG {cog_work_item.asset_key}: {e}")
            return None

    async def _process_single_item(
        self, 
        stac_item: Any, 
        max_concurrent_cog_requests: int
    ) -> List[Dict[str, Any]]:
        """
        Process a single STAC item - maintains compatibility with existing logic.
        
        Args:
            stac_item: STAC item to process
            max_concurrent_cog_requests: Max concurrent COG requests
            
        Returns:
            List of unified records
        """
        unified_records = []
        
        try:
            # Check for existing keys if checker is available
            only_keys = None
            if self.existing_key_checker:
                scene_id = getattr(stac_item, 'id', None)
                if scene_id:
                    existing_keys = self.existing_key_checker(scene_id, None)
                    if existing_keys:
                        # Get all asset keys and subtract existing ones
                        all_keys = set(stac_item.assets.keys()) if hasattr(stac_item, 'assets') else set()
                        missing_keys = all_keys - existing_keys
                        if not missing_keys:
                            logger.debug(f"Skipping {scene_id} - all COG keys already exist")
                            return []
                        only_keys = missing_keys
            
            # Parse COG headers
            if self.use_fast_parser:
                cog_records = await self.cog_processor.parse_cog_headers_for_item_fast(
                    stac_item, max_concurrent_cog_requests, only_keys
                )
            else:
                cog_records = await self.cog_processor.parse_cog_headers_for_item(
                    stac_item, max_concurrent_cog_requests, only_keys
                )
            
            # Create unified records
            if cog_records:
                # Flatten STAC item once
                item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
                
                # Inject federation metadata if available
                if hasattr(self, 'stac_api_url') and self.stac_api_url:
                    item_dict['stac_api_url'] = self.stac_api_url
                
                base_scene = self.unified_schema.flatten_stac_item(item_dict)
                
                # Create unified records (one per COG asset)
                for cog_record in cog_records:
                    cog = {
                        "cog_key": cog_record.get("asset_key"),
                        "cog_href": cog_record.get("asset_href"),
                        "cog_title": cog_record.get("asset_title"),
                        "cog_roles": cog_record.get("asset_roles", []),
                        "cog_width": cog_record.get("cog_width"),
                        "cog_height": cog_record.get("cog_height"),
                        "cog_tile_width": cog_record.get("cog_tile_width"),
                        "cog_tile_height": cog_record.get("cog_tile_height"),
                        "cog_dtype": cog_record.get("cog_dtype"),
                        "cog_compression": cog_record.get("cog_compression"),
                        "cog_predictor": cog_record.get("cog_predictor"),
                        "cog_crs": cog_record.get("cog_crs"),
                        "cog_transform": cog_record.get("cog_transform"),
                        "cog_tile_offsets": cog_record.get("cog_tile_offsets"),
                        "cog_tile_byte_counts": cog_record.get("cog_tile_byte_counts"),
                        "cog_scale": cog_record.get("cog_scale") or cog_record.get("scale"),
                        "cog_offset": cog_record.get("cog_offset") or cog_record.get("offset"),
                    }
                    unified_record = {**base_scene, **cog}
                    unified_records.append(unified_record)
                    
        except Exception as e:
            logger.error(f"Error processing STAC item {getattr(stac_item, 'id', 'unknown')}: {e}")
        
        return unified_records

    async def _output_collector(
        self,
        receive_output: MemoryObjectReceiveStream,
        batch_size: int,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Collect processed records into batches for output.

        Args:
            receive_output: Stream to receive processed records
            batch_size: Number of records per batch

        Yields:
            Tuple of (batch_stats, unified_records)
        """
        logger.debug("Starting output collector")
        async with receive_output:
            batch_records = []
            batch_start_time = time.time()

            async for record in receive_output:
                batch_records.append(record)

                if len(batch_records) >= batch_size:
                    # Create batch statistics compatible with traditional processor
                    batch_stats = {
                        'stac_items_processed': len(batch_records),  # Match traditional format
                        'cog_assets_processed': len(batch_records),  # Assume 1:1 for now
                        'unified_records_written': len(batch_records),
                        'batch_time': time.time() - batch_start_time,
                        'records_per_second': len(batch_records) / (time.time() - batch_start_time),
                        'errors': [],  # Compatibility with existing code
                    }

                    yield batch_stats, batch_records

                    # Reset for next batch
                    batch_records = []
                    batch_start_time = time.time()

            # Yield remaining records
            if batch_records:
                batch_stats = {
                    'stac_items_processed': len(batch_records),  # Match traditional format
                    'cog_assets_processed': len(batch_records),  # Assume 1:1 for now
                    'unified_records_written': len(batch_records),
                    'batch_time': time.time() - batch_start_time,
                    'records_per_second': len(batch_records) / (time.time() - batch_start_time) if time.time() - batch_start_time > 0 else 0,
                    'errors': [],  # Compatibility with existing code
                }
                yield batch_stats, batch_records

        logger.debug("Output collector finished")

    async def _monitor(self, send_stream, receive_stream):
        """
        Monitor streaming statistics and log progress.

        Args:
            send_stream: Producer stream for monitoring
            receive_stream: Consumer stream for monitoring
        """
        while True:
            await anyio.sleep(self.monitoring_interval)

            # Log current statistics
            logger.info(
                f"Streaming Stats: {self.stats.items_processed} items, "
                f"{self.stats.records_created} records, "
                f"{self.stats.items_per_second:.1f} items/sec, "
                f"{self.stats.records_per_second:.1f} records/sec, "
                f"{self.stats.errors} errors"
            )

            # Check if streams are done
            try:
                send_stats = send_stream.statistics()
                receive_stats = receive_stream.statistics()

                if (send_stats.current_buffer_used == 0 and
                    send_stats.open_receive_streams == 0 and
                    receive_stats.current_buffer_used == 0 and
                    receive_stats.open_receive_streams == 0):
                    logger.debug("Streams completed, stopping monitor")
                    break
            except Exception:
                # Streams might be closed
                break

    async def process_stac_items_in_batches(
        self,
        stac_items: AsyncIterator,
        batch_size: int,
        max_concurrent_cog_requests: int = 200,
        max_concurrent_stac_items: int = 20,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Process STAC items using streaming producer-consumer pattern.

        This method maintains compatibility with the existing StacProcessor interface
        while using tiff-dumper's streaming architecture for better performance.

        Args:
            stac_items: Iterator of STAC items to process
            batch_size: Number of records per output batch
            max_concurrent_cog_requests: Max concurrent COG header requests
            max_concurrent_stac_items: Max concurrent STAC items (used for consumer count)

        Yields:
            Tuple of (batch_stats, unified_records) for each batch
        """
        logger.info(f"Starting streaming STAC processing with {self.n_consumers} consumers")

        # Simplified approach without task groups to avoid scope issues
        tasks = []
        try:
            # Create memory streams
            send_items, receive_items = create_memory_object_stream(
                self.max_buffer_size, item_type=dict  # Now sending unified records
            )
            send_output, receive_output = create_memory_object_stream(
                self.max_buffer_size, item_type=dict
            )

            # Start tasks manually without task group

            # Start monitoring task
            monitor_task = asyncio.create_task(self._monitor(send_items, receive_output))
            tasks.append(monitor_task)

            # Start consumer tasks
            consumer_streams = [(receive_items, send_output)]
            for _ in range(self.n_consumers - 1):
                consumer_streams.append((receive_items.clone(), send_output.clone()))

            for receive_stream, send_stream in consumer_streams:
                consumer_task = asyncio.create_task(self._consumer(receive_stream, send_stream))
                tasks.append(consumer_task)

            # Start producer task
            producer_task = asyncio.create_task(
                self._producer(
                    send_items,
                    stac_items,
                    max_concurrent_cog_requests,
                    max_concurrent_stac_items
                )
            )
            tasks.append(producer_task)

            # Yield batches as they're ready from output collector
            async for batch_stats, unified_records in self._output_collector(receive_output, batch_size):
                yield batch_stats, unified_records

        except Exception as exc:
            logger.error(f"Streaming processing error: {exc}")
        finally:
            # Clean up tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

        logger.info(f"Streaming processing completed: {self.stats.items_processed} items, {self.stats.records_created} records")
