# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Performance configuration inspired by tiff-dumper's configuration patterns.

This module provides configuration-driven performance tuning for the
data-marketplace ingestion pipeline, allowing easy optimization for
different deployment scenarios without code changes.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import os


@dataclass
class COGParsingConfig:
    """Configuration for COG header parsing performance."""
    use_fast_parser: bool = True
    max_concurrent_requests: int = 200
    request_timeout_seconds: float = 30.0
    max_retries: int = 3
    chunk_size_bytes: int = 2048  # For fast parser header requests
    
    @classmethod
    def for_scenario(cls, scenario: str) -> "COGParsingConfig":
        """Create optimized config for specific scenarios."""
        if scenario == "high_throughput":
            return cls(
                use_fast_parser=True,
                max_concurrent_requests=500,
                request_timeout_seconds=15.0,
                max_retries=2,
                chunk_size_bytes=1024,
            )
        elif scenario == "reliable":
            return cls(
                use_fast_parser=True,
                max_concurrent_requests=100,
                request_timeout_seconds=60.0,
                max_retries=5,
                chunk_size_bytes=4096,
            )
        elif scenario == "memory_constrained":
            return cls(
                use_fast_parser=True,
                max_concurrent_requests=50,
                request_timeout_seconds=45.0,
                max_retries=3,
                chunk_size_bytes=1024,
            )
        else:
            return cls()  # Default


@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing performance."""
    processor_type: str = "traditional"  # traditional, streaming, bulk
    batch_size: int = 5000
    bulk_batch_size: int = 50000
    max_concurrent_stac_items: int = 20
    memory_pressure_threshold_mb: float = 2000.0
    gc_interval_batches: int = 10
    monitoring_interval_seconds: float = 10.0
    
    @classmethod
    def for_scenario(cls, scenario: str) -> "BatchProcessingConfig":
        """Create optimized config for specific scenarios."""
        if scenario == "high_throughput":
            return cls(
                processor_type="bulk",
                batch_size=10000,
                bulk_batch_size=100000,
                max_concurrent_stac_items=50,
                memory_pressure_threshold_mb=4000.0,
                gc_interval_batches=5,
                monitoring_interval_seconds=5.0,
            )
        elif scenario == "memory_constrained":
            return cls(
                processor_type="streaming",
                batch_size=1000,
                bulk_batch_size=10000,
                max_concurrent_stac_items=10,
                memory_pressure_threshold_mb=1000.0,
                gc_interval_batches=5,
                monitoring_interval_seconds=15.0,
            )
        elif scenario == "balanced":
            return cls(
                processor_type="traditional",
                batch_size=5000,
                bulk_batch_size=50000,
                max_concurrent_stac_items=20,
                memory_pressure_threshold_mb=2000.0,
                gc_interval_batches=10,
                monitoring_interval_seconds=10.0,
            )
        else:
            return cls()  # Default


@dataclass
class DeltaWriteConfig:
    """Configuration for Delta Lake write performance."""
    prefer_append_mode: bool = True
    enable_optimization: bool = True
    partition_columns: list = field(default_factory=lambda: ["year", "month"])
    enable_bloom_filters: bool = True
    enable_dictionary_encoding: bool = True
    write_timeout_seconds: float = 300.0
    
    @classmethod
    def for_scenario(cls, scenario: str) -> "DeltaWriteConfig":
        """Create optimized config for specific scenarios."""
        if scenario == "high_throughput":
            return cls(
                prefer_append_mode=True,
                enable_optimization=True,
                partition_columns=["year", "month"],
                enable_bloom_filters=True,
                enable_dictionary_encoding=True,
                write_timeout_seconds=600.0,
            )
        elif scenario == "storage_optimized":
            return cls(
                prefer_append_mode=False,  # Use MERGE for deduplication
                enable_optimization=True,
                partition_columns=["year", "month", "collection"],
                enable_bloom_filters=True,
                enable_dictionary_encoding=True,
                write_timeout_seconds=900.0,
            )
        else:
            return cls()  # Default


@dataclass
class PerformanceConfig:
    """Complete performance configuration for data-marketplace."""
    cog_parsing: COGParsingConfig = field(default_factory=COGParsingConfig)
    batch_processing: BatchProcessingConfig = field(default_factory=BatchProcessingConfig)
    delta_write: DeltaWriteConfig = field(default_factory=DeltaWriteConfig)
    
    @classmethod
    def for_scenario(cls, scenario: str) -> "PerformanceConfig":
        """Create complete optimized config for deployment scenarios."""
        return cls(
            cog_parsing=COGParsingConfig.for_scenario(scenario),
            batch_processing=BatchProcessingConfig.for_scenario(scenario),
            delta_write=DeltaWriteConfig.for_scenario(scenario),
        )
    
    @classmethod
    def from_environment(cls) -> "PerformanceConfig":
        """Load configuration from environment variables."""
        scenario = os.getenv("DATA_MARKETPLACE_SCENARIO", "balanced")
        config = cls.for_scenario(scenario)
        
        # Override with specific environment variables if present
        if os.getenv("COG_MAX_CONCURRENT"):
            config.cog_parsing.max_concurrent_requests = int(os.getenv("COG_MAX_CONCURRENT"))
        
        if os.getenv("BATCH_SIZE"):
            config.batch_processing.batch_size = int(os.getenv("BATCH_SIZE"))
        
        if os.getenv("BULK_BATCH_SIZE"):
            config.batch_processing.bulk_batch_size = int(os.getenv("BULK_BATCH_SIZE"))
        
        if os.getenv("PROCESSOR_TYPE"):
            config.batch_processing.processor_type = os.getenv("PROCESSOR_TYPE")
        
        if os.getenv("MEMORY_THRESHOLD_MB"):
            config.batch_processing.memory_pressure_threshold_mb = float(os.getenv("MEMORY_THRESHOLD_MB"))
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for logging/debugging."""
        return {
            "cog_parsing": {
                "use_fast_parser": self.cog_parsing.use_fast_parser,
                "max_concurrent_requests": self.cog_parsing.max_concurrent_requests,
                "request_timeout_seconds": self.cog_parsing.request_timeout_seconds,
            },
            "batch_processing": {
                "processor_type": self.batch_processing.processor_type,
                "batch_size": self.batch_processing.batch_size,
                "bulk_batch_size": self.batch_processing.bulk_batch_size,
                "max_concurrent_stac_items": self.batch_processing.max_concurrent_stac_items,
                "memory_pressure_threshold_mb": self.batch_processing.memory_pressure_threshold_mb,
            },
            "delta_write": {
                "prefer_append_mode": self.delta_write.prefer_append_mode,
                "enable_optimization": self.delta_write.enable_optimization,
                "enable_bloom_filters": self.delta_write.enable_bloom_filters,
            }
        }


# Predefined scenario configurations
SCENARIO_CONFIGS = {
    "development": PerformanceConfig.for_scenario("memory_constrained"),
    "testing": PerformanceConfig.for_scenario("balanced"),
    "production_small": PerformanceConfig.for_scenario("balanced"),
    "production_large": PerformanceConfig.for_scenario("high_throughput"),
    "cloud_burstable": PerformanceConfig.for_scenario("memory_constrained"),
    "cloud_compute_optimized": PerformanceConfig.for_scenario("high_throughput"),
}


def get_recommended_config(
    dataset_size: str = "medium",
    infrastructure: str = "cloud",
    priority: str = "balanced"
) -> PerformanceConfig:
    """
    Get recommended configuration based on deployment characteristics.
    
    Args:
        dataset_size: "small" (<10k items), "medium" (10k-100k), "large" (>100k)
        infrastructure: "local", "cloud", "cloud_burstable", "cloud_compute"
        priority: "speed", "memory", "balanced", "storage"
    
    Returns:
        Optimized PerformanceConfig
    """
    if dataset_size == "large" and infrastructure in ["cloud", "cloud_compute"] and priority == "speed":
        return PerformanceConfig.for_scenario("high_throughput")
    elif infrastructure == "cloud_burstable" or priority == "memory":
        return PerformanceConfig.for_scenario("memory_constrained")
    elif priority == "storage":
        return PerformanceConfig.for_scenario("storage_optimized")
    else:
        return PerformanceConfig.for_scenario("balanced")
