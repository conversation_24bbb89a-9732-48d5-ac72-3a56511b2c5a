# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Fast COG header parser inspired by tiff-dumper performance techniques.

This module provides a high-performance alternative to the existing COG parser
for bulk ingestion scenarios where maximum throughput is needed.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
import aiohttp
import struct
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class FastCogMetadata:
    """Minimal COG metadata for fast parsing."""
    width: int
    height: int
    tile_width: int
    tile_height: int
    dtype: str
    compression: int
    crs: Optional[int] = None
    transform: Optional[tuple] = None

    def to_dict(self) -> dict:
        """Convert to dictionary format compatible with CogMetadata.to_dict()."""
        # Map compression codes to strings (simplified)
        compression_map = {
            1: "none",
            5: "lzw",
            7: "jpeg",
            8: "deflate",
            32773: "packbits",
        }
        compression_str = compression_map.get(self.compression, f"unknown_{self.compression}")

        return {
            "cog_width": int(self.width),
            "cog_height": int(self.height),
            "cog_tile_width": int(self.tile_width),
            "cog_tile_height": int(self.tile_height),
            "cog_dtype": str(self.dtype),
            "cog_crs": f"EPSG:{self.crs}" if self.crs else None,
            "cog_predictor": None,  # Not parsed in fast mode
            "cog_transform": self.transform,
            "cog_compression": compression_str,
            "cog_tile_offsets": None,  # Not parsed in fast mode
            "cog_tile_byte_counts": None,  # Not parsed in fast mode
            "cog_scale": None,  # Not parsed in fast mode
            "cog_offset": 0.0,  # Default offset
        }


class FastCOGParser:
    """
    High-performance COG parser inspired by tiff-dumper techniques.
    
    Optimizations:
    - Minimal HTTP requests (only essential headers)
    - Simplified TIFF parsing (skip complex tags)
    - High concurrency with connection pooling
    - Streaming architecture for bulk processing
    """
    
    def __init__(self, max_concurrent: int = 1000, timeout: int = 30):
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.session = None
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Essential TIFF tags only
        self.essential_tags = {
            256: "width",           # ImageWidth
            257: "height",          # ImageLength  
            258: "bits_per_sample", # BitsPerSample
            259: "compression",     # Compression
            322: "tile_width",      # TileWidth
            323: "tile_height",     # TileLength
            339: "sample_format",   # SampleFormat
        }
        
        # Data type mapping (simplified)
        self.dtype_map = {
            (1, 8): "uint8",
            (1, 16): "uint16", 
            (1, 32): "uint32",
            (2, 16): "int16",
            (2, 32): "int32",
            (3, 32): "float32",
            (3, 64): "float64",
        }

    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=100,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "data-marketplace-fast-parser/1.0"}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def parse_cog_header_fast(self, url: str) -> Optional[FastCogMetadata]:
        """
        Fast COG header parsing with minimal HTTP requests.
        
        Only extracts essential metadata needed for ingestion.
        """
        async with self.semaphore:
            try:
                # Read minimal header (first 1KB should be enough for most TIFFs)
                async with self.session.get(
                    url, 
                    headers={"Range": "bytes=0-1023"}
                ) as response:
                    if response.status != 206:  # Partial content
                        return None
                    
                    header_bytes = await response.read()
                
                # Quick TIFF validation and parsing
                if len(header_bytes) < 16:
                    return None
                
                # Check byte order
                big_endian = header_bytes[0:2] == b"MM"
                endian = ">" if big_endian else "<"
                
                # Parse version and IFD offset
                version = struct.unpack(f"{endian}H", header_bytes[2:4])[0]
                if version not in (42, 43):  # Standard TIFF versions
                    return None
                
                if version == 42:
                    ifd_offset = struct.unpack(f"{endian}L", header_bytes[4:8])[0]
                    entry_size = 12
                else:  # BigTIFF
                    ifd_offset = struct.unpack(f"{endian}Q", header_bytes[8:16])[0]
                    entry_size = 20
                
                # Parse IFD entries (simplified)
                if ifd_offset + 100 > len(header_bytes):
                    # Need more data - read additional bytes
                    async with self.session.get(
                        url,
                        headers={"Range": f"bytes={ifd_offset}-{ifd_offset + 512}"}
                    ) as response:
                        if response.status != 206:
                            return None
                        ifd_bytes = await response.read()
                else:
                    ifd_bytes = header_bytes[ifd_offset:]
                
                # Parse essential tags only
                if len(ifd_bytes) < 2:
                    return None
                    
                entry_count = struct.unpack(f"{endian}H", ifd_bytes[0:2])[0]
                entry_count = min(entry_count, 50)  # Limit to prevent excessive parsing
                
                tags = {}
                for i in range(entry_count):
                    offset = 2 + (i * entry_size)
                    if offset + entry_size > len(ifd_bytes):
                        break
                        
                    entry = ifd_bytes[offset:offset + entry_size]
                    tag = struct.unpack(f"{endian}H", entry[0:2])[0]
                    
                    # Only parse essential tags
                    if tag in self.essential_tags:
                        type_id = struct.unpack(f"{endian}H", entry[2:4])[0]
                        count = struct.unpack(f"{endian}L", entry[4:8])[0]
                        
                        # Simple value extraction (assume values fit in entry)
                        if type_id == 3:  # SHORT
                            value = struct.unpack(f"{endian}H", entry[8:10])[0]
                        elif type_id == 4:  # LONG
                            value = struct.unpack(f"{endian}L", entry[8:12])[0]
                        else:
                            continue
                            
                        tags[tag] = value
                
                # Extract essential metadata
                width = tags.get(256, 0)
                height = tags.get(257, 0)
                tile_width = tags.get(322, width)
                tile_height = tags.get(323, height)
                compression = tags.get(259, 1)
                bits_per_sample = tags.get(258, 8)
                sample_format = tags.get(339, 1)
                
                if width == 0 or height == 0:
                    return None
                
                # Determine data type
                dtype = self.dtype_map.get((sample_format, bits_per_sample), "uint8")
                
                return FastCogMetadata(
                    width=int(width),
                    height=int(height),
                    tile_width=int(tile_width),
                    tile_height=int(tile_height),
                    dtype=str(dtype),
                    compression=int(compression),
                )
                
            except Exception as e:
                logger.debug(f"Fast parse failed for {url}: {e}")
                return None

    async def parse_batch_fast(self, urls: List[str]) -> List[Optional[FastCogMetadata]]:
        """Parse a batch of COG headers with high concurrency."""
        tasks = [self.parse_cog_header_fast(url) for url in urls]
        return await asyncio.gather(*tasks, return_exceptions=True)


async def parse_cog_headers_fast(urls: List[str], max_concurrent: int = 1000) -> List[Optional[FastCogMetadata]]:
    """
    Convenience function for fast COG header parsing.
    
    Args:
        urls: List of COG URLs to parse
        max_concurrent: Maximum concurrent requests
        
    Returns:
        List of FastCogMetadata objects (None for failed parses)
    """
    async with FastCOGParser(max_concurrent=max_concurrent) as parser:
        return await parser.parse_batch_fast(urls)
