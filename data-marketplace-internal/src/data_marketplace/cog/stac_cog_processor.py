# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
STAC-specific COG processing functionality.

This module extends the existing COG parser to handle STAC-specific
COG processing workflows and asset management.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from pystac import Item

from data_marketplace.cog import AsyncCOGHeaderParser
from data_marketplace.cog.fast_parser import FastCOGParser

logger = logging.getLogger(__name__)


class StacCogProcessor:
    """Handles COG processing for STAC items."""

    def __init__(self, use_fast_parser: bool = False):
        """Initialize STAC COG processor.

        Args:
            use_fast_parser: If True, use FastCOGParser for better performance
        """
        self.cog_parser = AsyncCOGHeaderParser()
        self.use_fast_parser = use_fast_parser

    async def parse_cog_headers_for_item(
        self,
        stac_item: Item,
        max_concurrent_requests: int,
        parser: Optional[AsyncCOGHeaderParser] = None,
        only_keys: Optional[set] = None,
    ) -> List[Dict[str, Any]]:
        """
        Parse COG headers for all relevant assets in a STAC item.

        Args:
            stac_item: STAC item to process
            max_concurrent_requests: Max concurrent COG requests
            only_keys: Optional set of asset keys to restrict parsing to (for retry)

        Returns:
            List of COG metadata records
        """
        cog_records = []

        # Filter for COG assets (and optionally restrict to only_keys)
        cog_assets = {
            key: asset
            for key, asset in stac_item.assets.items()
            if (only_keys is None or key in only_keys) and self.is_cog_asset(asset)
        }

        if not cog_assets:
            # Log available assets to help diagnose filtering
            try:
                asset_summ = {k: (str(v.media_type).lower() if hasattr(v, 'media_type') else None, str(v.href)[:120]) for k, v in stac_item.assets.items()}
                logger.debug(f"No COG assets found in STAC item {stac_item.id}. Available assets: {asset_summ}")
            except Exception:
                logger.debug(f"No COG assets found in STAC item {stac_item.id} (failed to summarize assets)")
            return cog_records

        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent_requests)

        async with AsyncCOGHeaderParser(
            max_concurrent=max_concurrent_requests
        ) as parser:

            async def parse_single_cog(
                asset_key: str, asset
            ) -> Optional[Dict[str, Any]]:
                """Parse a single COG asset."""
                async with semaphore:
                    try:
                        url = asset.href
                        logger.debug(f"Parsing COG header for {asset_key}: {url}")

                        # Parse COG header using optimized parser
                        cog_meta_obj = await parser.parse_cog_header(url)

                        if cog_meta_obj:
                            cog_meta = cog_meta_obj.to_dict()

                            # Extract scale/offset from asset metadata (raster:bands)
                            scale, offset = self.extract_scale_offset_from_asset(asset)
                            if scale is not None and "cog_scale" not in cog_meta:
                                cog_meta["cog_scale"] = scale
                            if offset is not None and "cog_offset" not in cog_meta:
                                cog_meta["cog_offset"] = offset

                            # Create enhanced COG record
                            cog_record = {
                                "asset_key": asset_key,
                                "asset_href": url,
                                "asset_media_type": asset.media_type,
                                "asset_roles": list(asset.roles) if asset.roles else [],
                                "asset_title": asset.title,
                                "asset_description": asset.description,
                                **cog_meta,
                            }

                            logger.debug(f"Successfully parsed COG for {asset_key}")
                            return cog_record
                        else:
                            logger.debug(
                                f"Failed to parse COG header for {asset_key} at {url}"
                            )
                            return None

                    except Exception as e:
                        logger.debug(f"Error parsing COG for {asset_key}: {e}")
                        return None

            # Process all COG assets in parallel
            tasks = [parse_single_cog(key, asset) for key, asset in cog_assets.items()]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Collect successful results
            for result in results:
                if isinstance(result, asyncio.CancelledError):
                    raise result
                if isinstance(result, Exception):
                    logger.error(f"COG parsing task failed: {result}")
                elif result is not None:
                    cog_records.append(result)

        logger.debug(
            f"Parsed {len(cog_records)} COG assets for STAC item {stac_item.id}"
        )
        return cog_records

    async def parse_cog_headers_for_item_fast(
        self,
        stac_item: Item,
        max_concurrent_requests: int,
        only_keys: Optional[set] = None,
    ) -> List[Dict[str, Any]]:
        """
        Fast COG header parsing for bulk scenarios.

        Uses simplified parsing for maximum throughput.
        """
        cog_records = []

        # Filter for COG assets
        cog_assets = {
            key: asset
            for key, asset in stac_item.assets.items()
            if (only_keys is None or key in only_keys) and self.is_cog_asset(asset)
        }

        if not cog_assets:
            return cog_records

        # Extract URLs for batch processing
        urls = [asset.href for asset in cog_assets.values()]
        asset_keys = list(cog_assets.keys())

        # Use fast parser for batch processing
        async with FastCOGParser(max_concurrent=max_concurrent_requests) as fast_parser:
            results = await fast_parser.parse_batch_fast(urls)

        # Convert results to COG records
        for i, (asset_key, asset) in enumerate(cog_assets.items()):
            result = results[i] if i < len(results) else None

            if result and not isinstance(result, Exception):
                # Convert FastCogMetadata to standard format
                cog_meta = result.to_dict()

                # Extract scale/offset from asset metadata if available
                scale, offset = self.extract_scale_offset_from_asset(asset)
                if scale is not None and "cog_scale" not in cog_meta:
                    cog_meta["cog_scale"] = scale
                if offset is not None and "cog_offset" not in cog_meta:
                    cog_meta["cog_offset"] = offset

                # Create enhanced COG record (same format as regular parser)
                cog_record = {
                    "asset_key": asset_key,
                    "asset_href": asset.href,
                    "asset_media_type": asset.media_type,
                    "asset_roles": list(asset.roles) if asset.roles else [],
                    "asset_title": asset.title,
                    "asset_description": asset.description,
                    **cog_meta,
                }

                cog_records.append(cog_record)

        logger.debug(
            f"Fast parsed {len(cog_records)} COG assets for STAC item {stac_item.id}"
        )
        return cog_records

    def extract_scale_offset_from_asset(
        self, asset
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        Extract scale and offset from asset raster:bands extension.

        Args:
            asset: STAC asset object

        Returns:
            Tuple of (scale, offset) or (None, None) if not found
        """
        try:
            raster_bands = asset.extra_fields.get("raster:bands", [])
            if (
                raster_bands
                and isinstance(raster_bands, list)
                and len(raster_bands) > 0
            ):
                first_band = raster_bands[0]
                scale = first_band.get("scale", None)
                offset = first_band.get("offset", None)

                if scale is not None or offset is not None:
                    logger.debug(f"Found scale={scale}, offset={offset} for asset")
                    return scale, offset
        except Exception as e:
            logger.debug(f"Error extracting scale/offset from asset: {e}")

        return None, None

    def is_cog_asset(self, asset) -> bool:
        """
        Check if asset is a COG asset.

        Args:
            asset: STAC asset object

        Returns:
            True if asset is a COG, False otherwise
        """
        if not getattr(asset, "href", None):
            return False

        href = str(asset.href).lower()
        media = (str(getattr(asset, "media_type", "")).lower())

        # Accept HTTP(S) GeoTIFF/COG assets and ignore JP2
        if not (href.startswith("http://") or href.startswith("https://")):
            return False

        # Accept common GeoTIFF media types; tolerate missing media_type if extension matches
        geotiff_types = {
            "image/tiff",
            "image/tiff; application=geotiff",
            "image/tiff; profile=cloud-optimized",
            "image/geotiff",
        }
        if (media and media not in geotiff_types) and not href.endswith((".tif", ".tiff")):
            return False

        # Exclude JP2 QA assets explicitly
        if href.endswith(".jp2") or media == "image/jp2":
            return False

        return True

    def get_cog_asset_priority(self, asset_key: str, asset) -> int:
        """
        Get priority for COG asset processing (lower number = higher priority).

        Args:
            asset_key: Asset key name
            asset: STAC asset object

        Returns:
            Priority value (0 = highest priority)
        """
        # Prioritize certain asset types
        high_priority_keys = ["red", "green", "blue", "nir", "B04", "B03", "B02", "B08"]
        medium_priority_keys = ["swir", "B11", "B12", "coastal", "B01"]

        if asset_key.lower() in [k.lower() for k in high_priority_keys]:
            return 0
        elif asset_key.lower() in [k.lower() for k in medium_priority_keys]:
            return 1
        else:
            return 2

    def filter_priority_assets(
        self, assets: Dict[str, Any], max_assets: int = 10
    ) -> Dict[str, Any]:
        """
        Filter assets by priority to limit processing load.

        Args:
            assets: Dictionary of asset key -> asset object
            max_assets: Maximum number of assets to process

        Returns:
            Filtered dictionary of assets
        """
        if len(assets) <= max_assets:
            return assets

        # Sort by priority
        sorted_assets = sorted(
            assets.items(), key=lambda x: self.get_cog_asset_priority(x[0], x[1])
        )

        # Take top priority assets
        return dict(sorted_assets[:max_assets])

    async def validate_cog_accessibility(
        self, urls: List[str], timeout: float = 10.0
    ) -> Dict[str, bool]:
        """
        Validate that COG URLs are accessible (simple GET/HEAD using aiohttp inside parser context).
        """
        results = {}

        async with AsyncCOGHeaderParser(max_concurrent=10) as parser:
            async def check_url(url: str) -> Tuple[str, bool]:
                try:
                    # Perform a small range request as a proxy for accessibility
                    await parser._fetch_byte_range(url, 0, 1)
                    return url, True
                except Exception as e:
                    logger.debug(f"URL {url} not accessible: {e}")
                    return url, False

            tasks = [check_url(url) for url in urls]
            url_results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in url_results:
            if isinstance(result, Exception):
                logger.error(f"URL validation task failed: {result}")
            else:
                url, accessible = result
                results[url] = accessible

        return results

    async def parse_cog_headers_for_asset(
        self,
        asset_href: str,
        asset_key: str,
        asset_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Parse COG headers for a single asset (for streaming processor).

        Args:
            asset_href: COG URL
            asset_key: Asset key (e.g., 'red', 'blue')
            asset_data: Asset metadata

        Returns:
            List with single COG record
        """
        try:
            # Parse COG headers
            async with AsyncCOGHeaderParser(max_concurrent=1) as parser:
                cog_meta_obj = await parser.parse_cog_header(asset_href)

                if cog_meta_obj:
                    cog_meta = cog_meta_obj.to_dict()

                    # Create COG record
                    cog_record = {
                        "asset_key": asset_key,
                        "asset_href": asset_href,
                        "asset_title": asset_data.get("title"),
                        "asset_roles": asset_data.get("roles", []),
                        **cog_meta
                    }

                    return [cog_record]

            return []

        except Exception as e:
            logger.error(f"Error parsing COG headers for {asset_key}: {e}")
            return []

    async def parse_cog_headers_for_asset_fast(
        self,
        asset_href: str,
        asset_key: str,
        asset_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Parse COG headers for a single asset using fast parser (for streaming processor).

        Args:
            asset_href: COG URL
            asset_key: Asset key (e.g., 'red', 'blue')
            asset_data: Asset metadata

        Returns:
            List with single COG record
        """
        try:
            # Parse COG headers with fast parser
            async with FastCOGParser(max_concurrent=1) as fast_parser:
                cog_info = await fast_parser.parse_cog_header_fast(asset_href)

                if cog_info:
                    # Create COG record
                    cog_record = {
                        "asset_key": asset_key,
                        "asset_href": asset_href,
                        "asset_title": asset_data.get("title"),
                        "asset_roles": asset_data.get("roles", []),
                        **cog_info.to_dict()
                    }

                    return [cog_record]

            return []

        except Exception as e:
            logger.error(f"Error parsing COG headers for {asset_key} (fast): {e}")
            return []
