# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
COG metadata types for data marketplace.

Copied and adapted from rasteret for unified STAC ingestion.
"""

from __future__ import annotations
from dataclasses import dataclass
from typing import Optional, List, Union, Tuple
import numpy as np
import pyarrow as pa


@dataclass
class CogMetadata:
    """
    Metadata for a Cloud-Optimized GeoTIFF.

    Attributes:
        width (int): Image width in pixels
        height (int): Image height in pixels
        tile_width (int): Internal tile width
        tile_height (int): Internal tile height
        dtype (str): Data type as string (e.g., 'uint16', 'float32')
        crs (Optional[int]): Coordinate reference system EPSG code
        predictor (Optional[int]): Compression predictor
        transform (Optional[Tuple[float, ...]]): Affine transform coefficients (6 values)
        compression (Optional[int]): Compression type
        tile_offsets (Optional[List[int]]): Byte offsets to tiles
        tile_byte_counts (Optional[List[int]]): Size of each tile
        pixel_scale (Optional[Tuple[float, ...]]): Resolution in CRS units
        tiepoint (Optional[Tuple[float, ...]]): Reference point coordinates
    """

    width: int
    height: int
    tile_width: int
    tile_height: int
    dtype: str
    crs: Optional[int] = None
    predictor: Optional[int] = None
    transform: Optional[Tuple[float, ...]] = None
    compression: Optional[int] = None
    tile_offsets: Optional[List[int]] = None
    tile_byte_counts: Optional[List[int]] = None
    pixel_scale: Optional[Tuple[float, ...]] = None
    tiepoint: Optional[Tuple[float, ...]] = None

    def to_dict(self) -> dict:
        """Convert to dictionary for unified schema with proper type conversion."""
        # Convert compression integer to string representation
        compression_str = None
        if self.compression is not None:
            # Map common TIFF compression codes to strings
            compression_map = {
                1: "none",
                5: "lzw",
                7: "jpeg",
                8: "deflate",
                32773: "packbits"
            }
            compression_str = compression_map.get(self.compression, str(self.compression))

        # Ensure tile offsets and byte counts are properly typed
        tile_offsets = None
        if self.tile_offsets is not None:
            tile_offsets = [int(offset) for offset in self.tile_offsets]

        tile_byte_counts = None
        if self.tile_byte_counts is not None:
            tile_byte_counts = [int(count) for count in self.tile_byte_counts]

        # Ensure transform is properly typed as list of floats
        transform = None
        if self.transform is not None:
            transform = [float(val) for val in self.transform]

        return {
            "cog_width": int(self.width),
            "cog_height": int(self.height),
            "cog_tile_width": int(self.tile_width),
            "cog_tile_height": int(self.tile_height),
            "cog_dtype": str(self.dtype),
            "cog_crs": f"EPSG:{self.crs}" if self.crs else None,
            "cog_predictor": int(self.predictor) if self.predictor is not None else None,
            "cog_transform": transform,
            "cog_compression": compression_str,
            "cog_tile_offsets": tile_offsets,
            "cog_tile_byte_counts": tile_byte_counts,
            "cog_scale": float(self.pixel_scale[0]) if self.pixel_scale and len(self.pixel_scale) > 0 else None,
            "cog_offset": 0.0,  # Default offset
        }
