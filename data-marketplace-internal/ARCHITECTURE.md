# Data Marketplace Architecture

## Overview

This document describes the technical architecture of the unified STAC + COG ingestion system, including design decisions, optimization strategies, and implementation details.

## Architecture Decision: Unified Single Table

### Decision Summary

**We use ONE UNIFIED DELTA TABLE containing all STAC collections**, not separate tables per collection.

### Rationale

**Primary Use Case**: "Give me all data available for this part of the world that has X, Y property like resolution"

**Unified Table Approach:**
```sql
-- Single query across all collections - FAST
SELECT scene_id, collection, cog_href, cog_width, platform
FROM unified_stac_table 
WHERE s2_cell_id IN ('89c25c', '89c25d', '89c25e')  -- S2 cells for AOI
  AND datetime BETWEEN '2024-01-01' AND '2024-12-31'
  AND cog_width <= 10980  -- 10m resolution or better
  AND cloud_cover < 20.0
ORDER BY datetime DESC
```

**Benefits:**
- ✅ **Single table scan**: No expensive UNION operations
- ✅ **Unified spatial statistics**: All spatial/temporal filters work optimally
- ✅ **Better compression**: Dictionary encoding for repeated scene metadata
- ✅ **Simplified queries**: No joins or complex federation logic

## Schema Design

### Unified Schema (45 fields)

The schema combines STAC scene metadata with COG asset metadata in a single row per COG asset:

```python
# Scene Metadata (repeated per COG asset)
scene_id: string                    # STAC item ID
collection: string                  # Collection identifier  
datetime: timestamp[us, tz=UTC]     # Observation time
platform: string                   # Satellite platform
constellation: string              # Mission constellation
geometry: geoarrow.wkb             # Native GeoArrow geometry
bbox: struct<xmin, ymin, xmax, ymax> # 4-field bbox for statistics

# Spatial Indexing
s2_cell_id: string                 # Primary S2 cell (hex string)
s2_cells: list<string>             # All covering cells
s2_level: int8                     # S2 cell level

# Temporal Partitioning
year: int16                        # Partition key
month: int8                        # Partition key
day: int8                          # Day of month

# COG Asset Metadata (asset-specific)
cog_key: string                    # Asset key (e.g., "B04", "B08")
cog_href: string                   # COG URL
cog_width: int32                   # Image width in pixels
cog_height: int32                  # Image height in pixels
cog_dtype: string                  # Data type (uint16, float32, etc.)
cog_compression: string            # Compression type
cog_crs: string                    # Coordinate reference system
cog_transform: list<float64>       # Affine transform coefficients
cog_tile_offsets: list<int64>      # Tile byte offsets
cog_tile_byte_counts: list<int64>  # Tile byte counts
```

### Optimization Features

**Dictionary Encoding (12 columns):**
- `collection`, `platform`, `constellation`, `mission`
- `processing_level`, `product_type`, `cog_dtype`
- `cog_compression`, `cog_crs`
- High compression for repeated values

**Bloom Filters (6 columns):**
- `scene_id`, `cog_key`, `cog_href`
- `s2_cell_id`, `collection`, `platform`
- Fast filtering on high-cardinality columns

**Partitioning:**
- `year`, `month` for temporal queries
- Enables efficient date range filtering

## COG Header Parsing

### Real COG Parser (from rasteret)

The system uses real COG header parsing copied from rasteret:

```python
# AsyncCOGHeaderParser features:
- aiohttp-based HTTP client with connection pooling
- TTL and LRU caching for performance
- Concurrent parsing (up to 300 requests)
- Retry logic with exponential backoff
- TIFF tag parsing for complete metadata
```

### COG Metadata Extraction

```python
class CogMetadata:
    width: int                      # Image dimensions
    height: int
    tile_width: int                 # Internal tiling
    tile_height: int
    dtype: str                      # Data type
    crs: Optional[int]              # EPSG code
    transform: Optional[Tuple]      # Affine transform
    compression: Optional[int]      # Compression type
    tile_offsets: Optional[List]    # Tile layout
    tile_byte_counts: Optional[List]
```

## Performance Optimizations

### Parquet Settings (Research-Based)

**Row Group Size**: 100MB
- Optimal for ~100K STAC records
- DuckDB studies show 100K-1M rows optimal
- Better than 256MB for metadata workloads

**Compression**: Snappy
- Faster decompression than ZSTD for analytical workloads
- Optimal for dictionary-heavy STAC metadata
- Better read performance for frequent access

**Page Size**: Optimized for metadata
- Data page size: 1MB (default)
- Dictionary page size: 1MB
- Optimal for string-heavy STAC data

### Spatial Indexing Strategy

**S2 Cell-Based Indexing:**
- Global spatial coverage with anti-meridian safety
- Hierarchical structure for multi-resolution queries
- Hilbert curve ordering for spatial locality

**4-Field Bbox Struct:**
- Optimal Parquet statistics for spatial filtering
- Better than WKT/WKB for range queries
- Enables efficient predicate pushdown

### Concurrent Processing

**COG Header Parsing:**
- Up to 300 concurrent HTTP requests
- Connection pooling and keep-alive
- TTL cache (1 hour) for repeated URLs
- Batch processing for efficiency

**STAC Item Processing:**
- Configurable batch sizes (default: 100)
- Async processing with semaphores
- Error handling and retry logic
- Progress tracking and statistics

## Delta Lake Integration

### ACID Transactions

```python
# Write with optimized settings
write_deltalake(
    table_or_uri=unified_table_path,
    data=unified_table,
    mode="append",
    partition_by=["year", "month"],
    writer_properties=WriterProperties(
        data_page_size_limit=1024*1024,  # 1MB
        max_row_group_size=100*1024*1024,  # 100MB
        default_column_properties=ColumnProperties(
            bloom_filter_properties=BloomFilterProperties(
                enabled=True,
                fpp=0.1,  # 10% false positive rate
                ndv=1000  # Estimated distinct values
            )
        )
    )
)
```

### File Optimization

**Automatic Compaction:**
- Consolidates small files after ingestion
- Improves query performance
- Reduces metadata overhead

**Time Travel:**
- Full Delta Lake history tracking
- Rollback capabilities for data quality issues
- Audit trail for compliance

## Implementation Details

### Repository Structure

```
data-marketplace-internal/
├── src/data_marketplace/
│   ├── cog/                    # COG header parsing (from rasteret)
│   │   ├── types.py           # CogMetadata type
│   │   ├── parser.py          # AsyncCOGHeaderParser
│   │   └── __init__.py
│   └── ingestion/
│       ├── stac_schema.py     # UnifiedStacSchema
│       └── delta_stac_ingester.py  # DeltaStacIngester
└── scripts/
    ├── ingest_stac_unified.py      # Production ingestion
    ├── test_unified_ingestion.py   # Integration test
    └── test_cog_parser.py          # COG parser test
```

### Dependencies

**Core Dependencies:**
- `deltalake>=0.21.0`: Delta Lake operations
- `pyarrow>=18.0.0`: Arrow tables and Parquet
- `aiohttp>=3.9.0`: Async HTTP for COG parsing
- `pystac-client>=0.8.0`: STAC API client
- `cachetools>=5.3.0`: Caching for performance

**Spatial Dependencies:**
- `geoarrow-pyarrow>=0.1.0`: Native GeoArrow support
- `s2sphere>=0.2.5`: S2 spatial indexing

## Detailed Ingestion Process

### Current Batch Processing Flow

1. **STAC API Search**
   ```python
   # Query STAC API with filters
   search = catalog.search(
       collections=[collection_id],
       datetime=datetime_range,
       bbox=bbox
   )
   ```

2. **Batch Creation & Processing**
   ```python
   # Group STAC items into batches (default: 200 items)
   for batch in batches(stac_items, batch_size=200):
       # Process each STAC item sequentially
       for stac_item in batch:
           # Parse COG headers concurrently (max 50 concurrent)
           cog_metadata = await parse_cog_headers(stac_item.assets)
           # Create unified records (1 per COG asset)
           unified_records.extend(create_unified_records(stac_item, cog_metadata))

       # Write entire batch to Delta Lake
       write_deltalake(unified_table_path, unified_records, mode="append")
   ```

3. **Delta Lake Write Strategy**
   - **Mode**: Append to existing table
   - **Partitioning**: year/month partitions
   - **Row Groups**: 16MB (configurable)
   - **Target File Size**: 128MB
   - **Optimization**: Manual compaction at end

### Million-Scale Ingestion Challenges

#### Current Performance Analysis
- **Batch Size**: 200 items → 5,000 batches for 1M scenes
- **Processing Rate**: ~300-600 items/hour
- **Total Time**: 69-139 days for 1M scenes
- **File Count**: 5,000+ Parquet files before compaction

#### Critical Bottlenecks
1. **Sequential STAC Processing**: Only 1 item at a time
2. **Limited COG Concurrency**: 50 concurrent requests
3. **Small Batches**: Frequent writes create file explosion
4. **Row Group Inefficiency**: 16MB groups vs 128MB target files
5. **No Fault Tolerance**: No checkpointing or resume capability

## Optimization Strategies

### Single Machine Optimization (Recommended)

**Target Performance**: 1M scenes in 2-5 days

#### 1. Batch Size Optimization
```python
# Current: Small batches, frequent writes
batch_size = 200  # → 5,000 writes for 1M scenes

# Optimized: Large batches, fewer writes
batch_size = 2000-5000  # → 200-500 writes for 1M scenes
```

#### 2. Concurrent STAC Processing
```python
# Current: Sequential processing
for stac_item in batch:
    process_item(stac_item)

# Optimized: Parallel processing
async def process_batch_parallel(batch, max_concurrent=20):
    semaphore = asyncio.Semaphore(max_concurrent)
    tasks = [process_item_with_semaphore(item, semaphore) for item in batch]
    return await asyncio.gather(*tasks)
```

#### 3. Enhanced COG Concurrency
```python
# Current: 50 concurrent COG requests
max_concurrent_cog = 50

# Optimized: Higher concurrency for AWS proximity
max_concurrent_cog = 200-500  # When running near S3
```

#### 4. Streaming Processing
```python
# Current: Hold entire batch in memory
unified_records = []
for item in batch:
    unified_records.extend(process_item(item))
write_deltalake(table_path, unified_records)

# Optimized: Stream processing
async def stream_process_batch(batch, chunk_size=500):
    for chunk in chunks(batch, chunk_size):
        chunk_records = await process_chunk(chunk)
        write_deltalake(table_path, chunk_records, mode="append")
```

#### 5. Optimized Parquet Settings
```python
# Current: Misaligned row groups
ROW_GROUP_SIZE = 16 * 1024 * 1024  # 16MB

# Optimized: Aligned with target file size
ROW_GROUP_SIZE = 128 * 1024 * 1024  # 128MB
TARGET_FILE_SIZE = 128 * 1024 * 1024  # 128MB
```

#### 6. Deferred Compaction Strategy
```python
# Current: Auto-compaction during writes (slow)
delta_properties = {
    "delta.autoOptimize.optimizeWrite": "true",
    "delta.autoOptimize.autoCompact": "true"
}

# Optimized: Deferred compaction
delta_properties = {
    "delta.autoOptimize.optimizeWrite": "false",
    "delta.autoOptimize.autoCompact": "false"
}

# Manual compaction every 100,000 items
if items_processed % 100000 == 0:
    delta_table.optimize.compact()
```

### Distributed Processing with Ray (Future)

**When to Consider Ray:**
- **>10M scenes** (single machine takes weeks)
- **Time constraints** (need results in hours)
- **Available cluster** with good networking

#### Ray Architecture
```python
@ray.remote
class COGParser:
    def parse_batch(self, cog_urls):
        # Distributed COG header parsing
        return parsed_metadata

@ray.remote
def process_stac_batch(stac_items):
    # Distributed STAC processing
    return unified_records

# Coordinator ensures safe Delta Lake writes
class DeltaLakeCoordinator:
    def write_batch(self, records, partition_key):
        # Coordinate writes to avoid conflicts
        pass
```

#### Ray Challenges
- **Write Coordination**: Delta Lake concurrent write conflicts
- **Network Overhead**: COG results transfer between workers
- **Complexity**: Distributed debugging and monitoring
- **Cost**: Multiple machines vs single large instance

## Best Practices for Million-Scale

### 1. Infrastructure Recommendations
- **AWS Instance**: c5n.24xlarge (96 vCPUs, 192GB RAM) near S3
- **Network**: 100 Gbps networking for COG header parsing
- **Storage**: NVMe SSD for temporary processing
- **Monitoring**: CloudWatch for resource tracking

### 2. Configuration for Scale
```python
# Optimized settings for million-scale
BATCH_SIZE = 5000                    # Fewer writes
MAX_CONCURRENT_STAC = 20             # Parallel STAC processing
MAX_CONCURRENT_COG = 500             # High COG concurrency
ROW_GROUP_SIZE = 128 * 1024 * 1024   # 128MB row groups
COMPACTION_INTERVAL = 100000         # Compact every 100K items
CHECKPOINT_INTERVAL = 50000          # Save progress every 50K items
```

### 3. Monitoring & Observability
```python
# Progress tracking
total_items = get_total_item_count()
processed_items = 0
start_time = time.time()

def log_progress():
    elapsed = time.time() - start_time
    rate = processed_items / elapsed
    eta = (total_items - processed_items) / rate
    logger.info(f"Progress: {processed_items}/{total_items} "
                f"({rate:.1f} items/sec, ETA: {eta/3600:.1f}h)")
```

### 4. Error Handling & Recovery
```python
# Checkpointing for resume capability
def save_checkpoint(processed_items, last_item_id):
    checkpoint = {
        "processed_items": processed_items,
        "last_item_id": last_item_id,
        "timestamp": datetime.now().isoformat()
    }
    with open("ingestion_checkpoint.json", "w") as f:
        json.dump(checkpoint, f)

def resume_from_checkpoint():
    if os.path.exists("ingestion_checkpoint.json"):
        with open("ingestion_checkpoint.json") as f:
            return json.load(f)
    return None
```

## Ray vs Single Machine Decision

### Single Machine (Recommended for 1-5M scenes)
**Pros:**
- **Simplicity**: Easier debugging, monitoring, deployment
- **Cost**: Single large instance cheaper than cluster
- **Performance**: Can achieve 1M scenes in 2-5 days with optimization
- **Development Speed**: Faster to implement and optimize

**Cons:**
- **Scale Limit**: May struggle with >10M scenes
- **Single Point of Failure**: No distributed fault tolerance

### Ray Distributed (Consider for >10M scenes)
**Pros:**
- **Scale**: Can handle massive datasets (>10M scenes)
- **Speed**: Potential for hours instead of days
- **Fault Tolerance**: Distributed error handling

**Cons:**
- **Complexity**: Distributed system debugging and monitoring
- **Delta Lake Coordination**: Concurrent write conflicts
- **Network Overhead**: COG results transfer between workers
- **Cost**: Multiple machines + operational overhead

## Conclusion

**For Million-Scale Ingestion:**
1. **Start with single machine optimization** - can achieve 1M scenes in 2-5 days
2. **Focus on larger batches and better concurrency** - biggest performance gains
3. **Implement streaming and checkpointing** - for reliability and memory efficiency
4. **Consider Ray only if >10M scenes** or tight time constraints
5. **Run on AWS near S3** - network proximity critical for COG parsing performance

The current architecture is fundamentally sound but needs optimization for scale. The unified table approach is correct, but execution needs improvement for production workloads.
